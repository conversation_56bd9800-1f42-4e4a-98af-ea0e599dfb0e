<template>
    <div class="row documents-text">
        <DocumentsAwaitApproveLink
            :awaitApproveCount="awaitApproveCount"
            href="/finance/documents-text/new"
            name="New"
        />
        <div class="col-md-2">
            <strong class="d-inline-block mb-2 text-primary">&nbsp;</strong>
            <pre class="pt-4 sticky-top">{{ fullText }}</pre>
        </div>
        <div class="col-md-8">
            <strong class="d-block mb-2 text-primary">
                <span
                    v-if="info.country"
                    class="badge bg-warning float-end"
                >{{ info.country }}</span>
                <RouterLink
                    v-if="info.siteUser"
                    :to="{name: 'player', params: {siteUser: info.siteId + '-' + info.userId}}"
                    target="_blank"
                >Player</RouterLink>
                &nbsp;
                <RouterLink
                    v-if="info.siteUser"
                    :to="{name: 'documents', query: {siteId: info.siteId, userId: info.userId}}"
                    target="_blank"
                >Documents</RouterLink>
            </strong>
            <div
                class="border rounded progress-bar progress-bar-striped progress-bar-animated bg-secondary"
                role="progressbar"
                aria-valuenow="75"
                aria-valuemin="0"
                aria-valuemax="100"
            >
                <canvas
                    ref="canvas"
                    style="width: 100%"
                />
            </div>
            <div
                v-if="otherDocuments"
                class="row"
            >
                <div
                    v-for="doc in otherDocuments"
                    class="col-xl-3 position-relative"
                >
                    <a
                        v-if="doc.imgLoaded && doc.approved === null"
                        href="#"
                        @click.prevent="ignoreDocument(doc)"
                    >
                        <Icona
                            name="icn-ban"
                            class=" position-absolute mt-4 ms-2 text-danger"
                            aria-hidden="true"
                        />
                    </a>
                    <RouterLink
                        :href="'/finance/documents-text/edit/' + doc.id"
                        :to="{path: '/finance/documents-text/' + (directEdit ? 'edit' : 'new') + '/' + doc.id}"
                    >
                        <img
                            :src="doc.imgLoaded ? doc.url : undefined"
                            :class="{'bg-success': doc.approved === true, 'bg-danger': doc.approved === false}"
                            style="width: 100%"
                            class="img-responsive img-thumbnail mt-3"
                            @load="showNextDocument"
                            @error="showNextDocument"
                        >
                    </RouterLink>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="mb-3 sticky-top">
                <strong class="d-inline-block mb-2 text-primary">&nbsp;</strong>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(Mode.CHOOSE_SURNAME)"
                    >
                        <Icona
                            :name="mode === Mode.CHOOSE_SURNAME ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="surname"
                        name="surname"
                        :value="boxes.surname.text"
                        :enabled="boxes.surname.text !== undefined"
                        :isInvalid="loaded && approved === true && !boxes.surname.text"
                        inputClass="text-uppercase rounded-0"
                        liveSearchUrl="/finance/documents-text/autocomplete-surname"
                        placeholder="Surname"
                        class="form-control form-control-md"

                        @input="boxes.surname.text = $event"
                        @change="boxes.surname.text = $event"
                    />
                    <button
                        :disabled="boxes.surname.text === undefined"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(BoxType.SURNAME)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(Mode.CHOOSE_NAME)"
                    >
                        <Icona
                            :name="mode === Mode.CHOOSE_NAME ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="name"
                        name="name"
                        :value="boxes.name.text"
                        :enabled="boxes.name.text != null"
                        inputClass="text-uppercase rounded-0"
                        liveSearchUrl="/finance/documents-text/autocomplete-name"

                        placeholder="Name"
                        class="form-control form-control-md"

                        @input="boxes.name.text = $event"
                        @change="boxes.name.text = $event"
                    />
                    <button
                        :disabled="boxes.name.text == null"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(BoxType.NAME)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <button
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="switchMode(Mode.CHOOSE_PATRONYMIC)"
                    >
                        <Icona
                            :name="mode === Mode.CHOOSE_PATRONYMIC ? 'icn-hand-finger-up' : 'icn-wrench'"
                        />
                    </button>
                    <TextInputLiveSearch
                        id="patronymic"
                        name="patronymic"
                        :value="boxes.patronymic.text"
                        :enabled="boxes.patronymic.text !== undefined"
                        inputClass="text-uppercase rounded-0"
                        liveSearchUrl="/finance/documents-text/autocomplete-patronymic"

                        placeholder="Patronymic"
                        class="form-control form-control-md"

                        @input="boxes.patronymic.text = $event"
                        @change="boxes.patronymic.text = $event"
                    />
                    <button
                        :disabled="boxes.patronymic.text == null"
                        class="btn btn-outline-secondary focus-disabled"
                        type="button"
                        @click="banType(BoxType.PATRONYMIC)"
                    >
                        <Icona name="icn-ban" />
                    </button>
                </div>
                <div
                    v-if="!directEdit && Object.keys(prevFullName).length > 0"
                    class="input-group mb-3 flex-nowrap"
                >
                    <pre
                        class="form-control form-control-md text-uppercase"
                        style="height: auto; white-space: normal;"
                    ><span
                        v-for="(v, k) in prevFullName"
                        :class="[boxes[k].type === BoxType.TRASH ? 'text-secondary' : (boxes[k].text.toLowerCase().trim() === v ? 'fw-normal' : 'fw-bold')]"
                        class="d-block"
                    >
                            {{ v }}
                    </span></pre>
                    <button
                        class="btn btn-outline-secondary"
                        type="button"
                        @click="prevFullNameLoad"
                    >
                        <Icona name="icn-arrow-up" />
                    </button>
                </div>
                <div class="input-group mb-3 flex-nowrap">
                    <div
                        class="btn-group btn-group-toggle w-100"
                        data-toggle="buttons"
                    >
                        <label
                            :class="approved === false ? 'active' : ''"
                            class="btn btn-outline-secondary pointer"
                            @click="approved = false"
                        >
                            Ignore unreadable
                        </label>
                        <label
                            :class="approved ? 'active' : ''"
                            class="btn btn-outline-secondary pointer"
                            @click="approved = true"
                        >
                            Approve
                        </label>
                    </div>
                </div>
                <button
                    :disabled="!formValid"
                    type="button"
                    class="btn btn-primary btn-lg d-block"
                    @click="sendForm"
                >
                    {{ directEdit ? 'SAVE' : 'SEND' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Painter } from './painter'
import { TextBoundingBox, BoxType } from './text-bounding-box'
import TextInputLiveSearch from '@/components/form/types/text-input-live-search.vue'
import DocumentsAwaitApproveLink from '../documents-await-approve-link.vue'
import { defineComponent } from 'vue'
import { Icona } from '@/components'
import { NextWithReload, PointType, Values } from '@/types'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs.ts'

export enum Mode {
    DEFAULT = 'default',
    CHOOSE_SURNAME = 'surname',
    CHOOSE_NAME = 'name',
    CHOOSE_PATRONYMIC = 'patronymic'
}

const CONTROL_SIZE = 6

interface DocumentInfo {
    siteId: number
    userId: number
    siteUser: string
    country?: string
}

interface OtherDocument {
    id: string
    url: string
    approved: boolean | null
    imgLoaded: boolean
}

type TextOnPhoto = [BoxType | null, string, number, number, number, number, number, number, number, number] // 4 pairs of coordinates
type ItemName = 'new' | 'surname' | 'name' | 'patronymic'

interface ApiResponse {
    data: {
        url: string
        awaitApproveCount: number
        fullText: string
        siteId: number
        userId: number
        siteUser: string
        country?: string
        userDocumentId: string
        approved: boolean | null
        otherDocuments: Omit<OtherDocument, 'imgLoaded'>[]
        textBoundingBoxes: TextOnPhoto[]
        textBoundingBoxesManual?: TextOnPhoto[]
    }
}

export default defineComponent({
    components: {
        Icona,
        TextInputLiveSearch,
        DocumentsAwaitApproveLink,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(to.params))
    },
    beforeRouteUpdate (to, from, next) {
        if (from.params.userDocumentId || !to.params.userDocumentId) {
            const prev = this.prevFullName
            this.reload(to.params)
            this.prevFullName = prev
        }
        next()
    },
    props: {
        directEdit: {
            type: Boolean,
            default: false,
        },
    },
    setup() {
        return {
            Mode,
            BoxType,
            titleBreadcrumbs: useTitleBreadcrumbs(),
        }
    },
    data () {
        return {
            awaitApproveCount: 0,
            info: {} as DocumentInfo,
            loaded: false,
            trashEnabled: false,
            userDocumentId: undefined as string | undefined,
            boxes: {
                new: new TextBoundingBox(BoxType.TRASH),
                surname: new TextBoundingBox(BoxType.TRASH),
                name: new TextBoundingBox(BoxType.TRASH),
                patronymic: new TextBoundingBox(BoxType.TRASH),
            } as Record<ItemName, TextBoundingBox>,
            prevFullName: {} as Record<ItemName, string>,
            predictedBoxes: [] as TextBoundingBox[],
            mouse: { x: -1, y: -1 } as PointType,
            editableBox: undefined as TextBoundingBox | undefined,
            painter: undefined as Painter | undefined,
            fullText: undefined as string | undefined,
            mouseLeftDragging: false,
            mouseLeftIsDown: false,
            mouseRightIsDown: false,
            mouseRightDownPoint: false as PointType | false,
            mouseRightDragging: false,
            mode: Mode.DEFAULT,
            approved: null as boolean | null,
            activeControlDot: undefined as number | undefined,
            afterChoose: {
                [Mode.CHOOSE_SURNAME]: Mode.DEFAULT,
                [Mode.CHOOSE_NAME]: Mode.DEFAULT,
                [Mode.CHOOSE_PATRONYMIC]: Mode.DEFAULT,
            } as Record<Mode, Mode>,
            nextMode: Mode.DEFAULT,
            otherDocuments: [] as OtherDocument[],
        }
    },
    computed: {
        allBoxes (): TextBoundingBox[] {
            return [...Object.values(this.boxes), ...this.predictedBoxes]
        },
        formValid (): boolean {
            return Boolean(this.boxes.surname.text) || this.approved === false
        },
    },
    methods: {
        extractBoxFromTextPoints (p: TextOnPhoto): PointType[] {
            const pairs = p.slice(2) as number[];
            return pairs
                .filter((_x, i) => i % 2 === 0)
                .map((x, i) => [x, pairs[i * 2 + 1]])
                .map(d => ({
                    x: d[0] * this.painter!.resize,
                    y: d[1] * this.painter!.resize,
                }))
        },
        async reload (params: Values) {
            //Object.assign(this.$data, (this.$options.data as () => ComponentData)())
            this.painter = new Painter(this.$refs.canvas as HTMLCanvasElement)
            this.painter.on('mouseup', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseUpLeft, this.mouseUpRight))
            this.painter.on('mousemove', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseMove))
            this.painter.on('mousedown', (e: Event) => this.mouseChoose(e as MouseEvent, this.mouseDownLeft, this.mouseDownRight))

            const resp = await this.$fetch<ApiResponse>('/finance/documents-text/view', params)
            await this.painter!.loadImage(resp.data.url)

            this.loaded = true
            this.awaitApproveCount = resp.data.awaitApproveCount
            this.fullText = resp.data.fullText
            this.info = {
                siteId: resp.data.siteId,
                userId: resp.data.userId,
                siteUser: resp.data.siteUser,
                country: resp.data.country,
            }
            this.userDocumentId = resp.data.userDocumentId
            if (!this.directEdit && !this.userDocumentId) {
                await this.$router.push({ params: { userDocumentId: this.userDocumentId } })
            }

            this.titleBreadcrumbs.setTitle(this.info.siteUser)

            this.approved = resp.data.approved
            this.otherDocuments = resp.data.otherDocuments.map((doc): OtherDocument => {
                return { ...doc, imgLoaded: false }
            })

            resp.data.textBoundingBoxes.forEach(b => this.predictedBoxes.push(new TextBoundingBox(b[0], b[1], this.extractBoxFromTextPoints(b))))
            if (resp.data.textBoundingBoxesManual) {
                resp.data.textBoundingBoxesManual.forEach(b => {
                    if (b[0] === BoxType.SURNAME) {
                        this.boxes.surname.fill(-BoxType.SURNAME, b[1], this.extractBoxFromTextPoints(b))
                    } else if (b[0] === BoxType.NAME) {
                        this.boxes.name.fill(-BoxType.NAME, b[1], this.extractBoxFromTextPoints(b))
                    } else if (b[0] === BoxType.PATRONYMIC) {
                        this.boxes.patronymic.fill(-BoxType.PATRONYMIC, b[1], this.extractBoxFromTextPoints(b))
                    }
                })
            }
            if (this.approved === null) {
                this.afterChoose = {
                    [Mode.CHOOSE_SURNAME]: Mode.CHOOSE_NAME,
                    [Mode.CHOOSE_NAME]: Mode.CHOOSE_PATRONYMIC,
                    [Mode.CHOOSE_PATRONYMIC]: Mode.DEFAULT,
                    [Mode.DEFAULT]: Mode.DEFAULT,
                }
                this.switchMode(Mode.CHOOSE_SURNAME)
                this.approved = true
            }
            this.reDrawAll()
            this.showNextDocument()
        },
        isVisible (box: TextBoundingBox) {
            return box.type === null || (box.type < BoxType.TRASH && this.mode === Mode.DEFAULT)
        },
        mouseChoose (e: MouseEvent, leftButtonHandler?: (x: number, y: number) => void, rightButtonHandler?: (x: number, y: number) => void) {
            if (e.button === 0 && leftButtonHandler) {
                leftButtonHandler(...this.painter!.mousePosition(e))
            } else if (e.button === 2 && rightButtonHandler) {
                rightButtonHandler(...this.painter!.mousePosition(e))
            }
        },
        setBoxType (box: TextBoundingBox, type: BoxType | null) {
            switch (type) {
                case BoxType.SURNAME:
                    this.boxes.surname.fill(-type, box.text || '', box.polygon || [])
                    break
                case BoxType.NAME:
                    this.boxes.name.fill(-type, box.text || '', box.polygon || [])
                    break
                case BoxType.PATRONYMIC:
                    this.boxes.patronymic.fill(-type, box.text || '', box.polygon || [])
                    break
                case BoxType.TRASH:
                    if (this.editableBox !== box) {
                        box.type = type
                    }
                    return
                case null:
                    switch (box.type) {
                        case BoxType.SURNAME:
                            this.boxes.surname.fill(null, '', [])
                            break
                        case BoxType.NAME:
                            this.boxes.name.fill(null, '', [])
                            break
                        case BoxType.PATRONYMIC:
                            this.boxes.patronymic.fill(null, '', [])
                            break
                    }
                    if (this.editableBox === box) {
                        this.editableBox = undefined
                    }
                    box.type = type
                    return
                default:
                    throw new Error('invalid type')
            }
            box.type = type
            this.allBoxes
                .filter(b => b !== box && b.type === type)
                .forEach(b => b.type = null)
        },

        mouseDownRight (x: number, y: number) {
            this.mouseRightIsDown = true
            this.mouseRightDragging = false
            this.mouseRightDownPoint = { x, y }
        },
        mouseUpRight (x: number, y: number) {
            this.mouseRightIsDown = false

            const mp = this.mouseRightDownPoint
            this.mouseRightDownPoint = false

            const targets: Record<Mode, { box: ItemName, type: BoxType }> = {
                [Mode.CHOOSE_SURNAME]: { box: 'surname', type: BoxType.SURNAME },
                [Mode.CHOOSE_NAME]: { box: 'name', type: BoxType.NAME },
                [Mode.CHOOSE_PATRONYMIC]: { box: 'patronymic', type: BoxType.PATRONYMIC },
                [Mode.DEFAULT]: { box: 'new', type: BoxType.TRASH },
            }

            if (this.mode in targets && this.mode !== Mode.DEFAULT) {
                if (mp && Math.abs(x - mp.x) > 2 * CONTROL_SIZE && Math.abs(y - mp.y) > 2 * CONTROL_SIZE) {
                    this.predictedBoxes
                        .filter(box => box.type === targets[this.mode].type)
                        .forEach(box => this.setBoxType(box, null))

                    const targetBox = this.boxes[targets[this.mode].box]
                    targetBox.fill(-targets[this.mode].type as number, '', [
                        { x, y },
                        { x, y: mp.y },
                        { x: mp.x, y: mp.y },
                        { x: mp.x, y },
                    ])

                    this.switchMode(this.nextMode)
                }
            } else if (this.trashEnabled && this.editableBox === undefined && this.mode === Mode.DEFAULT && !this.mouseRightDragging) {
                this.allBoxes.filter(box => box.type === null && box.haveInnerPoint(x, y))
                    .forEach(box => this.setBoxType(box, BoxType.TRASH))
            }
            this.reDrawAll()
        },

        mouseDownLeft (x: number, y: number) {
            this.mouseLeftDragging = false
            this.mouseLeftIsDown = true

            this.allBoxes.some(box => {
                if (this.editableBox === box) {
                    this.activeControlDot = box.findControl(x, y, CONTROL_SIZE)
                    return true
                }

                return false
            })
        },
        mouseMove (x: number, y: number) {
            this.mouse = { x, y }

            if (this.mouseRightIsDown && this.mode !== Mode.DEFAULT) {
                this.mouseRightDragging = true
                if (this.mouseRightDownPoint) {
                    this.boxes.new.fill(null, '', [
                        { x, y },
                        { x, y: this.mouseRightDownPoint.y },
                        { x: this.mouseRightDownPoint.x, y: this.mouseRightDownPoint.y },
                        { x: this.mouseRightDownPoint.x, y },
                    ])
                }
            } else {
                this.boxes.new.fill(BoxType.TRASH, '', [])
            }

            if (this.mouseLeftIsDown) {
                this.mouseLeftDragging = true

                if (this.activeControlDot !== undefined && this.editableBox && this.editableBox.polygon) {
                    this.editableBox.polygon[this.activeControlDot] = this.mouse
                }
            } else {
                const overPolygon = this.allBoxes.some(box => {
                    if (
                        (this.mode === Mode.DEFAULT && box.type !== null && box.type < BoxType.TRASH) ||
                        (this.mode !== Mode.DEFAULT && box.type === null) &&
                        this.isVisible(box) &&
                        ((this.editableBox === box && box.findControl(x, y, CONTROL_SIZE) !== undefined) || (this.editableBox !== box && box.haveInnerPoint(x, y)))
                    ) {
                        this.painter!.cursor('pointer')
                        return true
                    }
                    return false
                })
                if (!overPolygon) {
                    this.painter!.cursor('default')
                }
            }
            this.reDrawAll()
        },
        mouseUpLeft (x: number, y: number) {
            this.mouseLeftIsDown = false
            this.activeControlDot = undefined
            if (this.mouseLeftDragging) {
                this.mouseLeftDragging = false
                return
            }

            const modeToType: Record<Mode, BoxType | null> = {
                [Mode.CHOOSE_SURNAME]: BoxType.SURNAME,
                [Mode.CHOOSE_NAME]: BoxType.NAME,
                [Mode.CHOOSE_PATRONYMIC]: BoxType.PATRONYMIC,
                [Mode.DEFAULT]: null,
            }

            if (this.mode in modeToType) {
                this.allBoxes.filter(b => b.type === null).filter(b => b.haveInnerPoint(x, y)).some(box => {
                    const targetType = modeToType[this.mode]
                    if (targetType !== undefined) {
                        this.setBoxType(box, targetType)
                        this.switchMode(this.nextMode)
                    }
                    return true
                })
            } else {
                const activeFound = this.allBoxes.filter(b => b.type !== null && b.type < 0).filter(b => b.haveInnerPoint(x, y)).some(box => {
                    this.editableBox = box
                    return true
                })
                if (!activeFound) {
                    this.editableBox = undefined
                }
            }

            this.reDrawAll()
        },

        banType (type: BoxType) {
            if (type === BoxType.SURNAME) {
                this.boxes.surname.fill(BoxType.TRASH, '', [])
            } else if (type === BoxType.NAME) {
                this.boxes.name.fill(BoxType.TRASH, '', [])
            } else if (type === BoxType.PATRONYMIC) {
                this.boxes.patronymic.fill(BoxType.TRASH, '', [])
            }

            this.allBoxes.filter(b => b.type === type).forEach(b => b.type = null)
            this.switchMode(Mode.DEFAULT)
        },
        switchMode (mode: Mode) {
            if (this.mode === Mode.DEFAULT && mode === Mode.DEFAULT) {
                this.editableBox = undefined
                this.reDrawAll()
                return
            } else if (this.mode === mode) {
                for (const m in this.afterChoose) {
                    this.afterChoose[m as Mode] = Mode.DEFAULT
                }
                this.switchMode(Mode.DEFAULT)
                return
            }

            this.mode = mode
            this.nextMode = this.afterChoose[mode] ? this.afterChoose[mode] : Mode.DEFAULT
            this.afterChoose[mode] = Mode.DEFAULT
            this.editableBox = undefined
            this.reDrawAll()
        },
        reDrawAll () {
            this.painter!.clear()
            this.allBoxes
                .filter(this.isVisible)
                .forEach(this.drawBox)
        },
        drawBox (box: TextBoundingBox) {
            //console.log(Object.values(box))
            if (box.polygon && box.polygon.length > 0) {
                this.painter!.drawPolygon(box.type !== null && box.type < BoxType.TRASH ? 'red' : 'black', box.polygon)
                if (this.editableBox === box) {
                    this.painter!.drawDots('rgba(44,62,80,0.5)', CONTROL_SIZE, box.polygon)
                }
                if (this.isTextVisible(box) && box.text && box.polygon.length >= 2) {
                    this.painter!.drawTextBetween(box.text, '14px Roboto', '#2c3e50', box.polygon[0], box.polygon[1])
                }
            }
        },
        isTextVisible (box: TextBoundingBox) {
            return this.editableBox === box || (box.haveInnerPoint(this.mouse.x, this.mouse.y) && !this.mouseLeftDragging)
        },
        async sendForm () {
            if (!this.formValid) {
                return
            }

            if (this.approved) {
                this.prevFullName = {} as Record<ItemName, string>;
                (['surname', 'name', 'patronymic'] as const).filter(itemName => {
                    return this.boxes[itemName].type !== BoxType.TRASH
                }).forEach(itemName => {
                    const box = this.boxes[itemName]
                    if (box.text) {
                        this.prevFullName[itemName] = box.text.toLowerCase().trim()
                    }
                })
            }
            const boxToPolygon = (box: TextBoundingBox) => [
                -(box.type || 0),
                box.text || '',
                ...(!box.polygon ? [] : [
                    box.polygon[0].x, box.polygon[0].y,
                    box.polygon[1].x, box.polygon[1].y,
                    box.polygon[2].x, box.polygon[2].y,
                    box.polygon[3].x, box.polygon[3].y,
                ]).map(v => Math.round(v / this.painter!.resize)),
            ] as TextOnPhoto

            const formData = {
                userDocumentId: this.userDocumentId,
                approved: this.approved,
                textBoundingBoxes: [] as TextOnPhoto[],
            }

            if (this.boxes.surname.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxes.surname))
            }
            if (this.boxes.name.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxes.name))
            }
            if (this.boxes.patronymic.text) {
                formData.textBoundingBoxes.push(boxToPolygon(this.boxes.patronymic))
            }

            await this.$fetch('/finance/documents-text/update', formData)
            if (!this.directEdit) {
                this.$router.push('/finance/documents-text/new')
            }
        },
        prevFullNameLoad () {
            Object.keys(this.prevFullName)
                .map(itemName => itemName as ItemName)
                .filter(itemName => {
                    return this.boxes[itemName].type !== BoxType.TRASH
                })
                .forEach(itemName => {
                    this.boxes[itemName].text = this.prevFullName[itemName]
                })
        },
        ignoreDocument (doc: OtherDocument) {
            doc.approved = false
            this.$fetch('/finance/documents-text/update', {
                userDocumentId: doc.id,
                approved: false,
            }).catch(() => {
                doc.approved = null
            })
        },
        showNextDocument () {
            this.otherDocuments
                .filter(v => !v.imgLoaded)
                .filter((_v, i) => i === 0)
                .forEach(v => v.imgLoaded = true)
        },
    },
})
</script>

<style lang="scss">
.documents-text {
    .focus-disabled {
        outline: none !important;
        box-shadow: none;
    }

    #name, #surname, #patronymic {
        text-transform: uppercase;
    }
}
</style>
