<template>
    <template v-for="(inputSet, setI) in inputsSets" :key="`inputSet${setI}`">
        {{ inputSet.title }}
        <div
            :ref="`ddSet${setI}`"
            class="form-params rounded-3 bg-tet"
            :class="{'bg-body-tertiary': setI === sortableActiveSet}"
            :data-block="setI"
        >
            <template v-for="(filter, _filterI) in inputSet.inputs" :key="`input${_filterI}`">
                <div
                    class="m-1 d-inline-flex w-auto draggable"
                    :data-name="filter.name"
                >
                    <button
                        type="button"
                        class="btn btn-outline-secondary hidden-on-form-params drag-helper"
                    >
                        {{ filter.title }}
                    </button>
                    <div class="position-relative hidden-on-form-list">
                        <button
                            type="button"
                            class="btn btn-sm form-params-button-with-validation-on-hover"
                            :class="filterClass(filter)"
                            :title="help[filter.name]"
                            :disabled="disabled"
                            @click="onParamClick(filter)"
                        >
                            {{ filter.title }}
                        </button>
                        <FormError v-if="errors[filter.name]" :error="errors[filter.name]" />
                    </div>
                </div>
                <HelpHint
                    :showEdit="hintsEditOpened"
                    :description="help[filter.name]"
                    @submit="onHintSubmit(filter.name, $event)"
                />
            </template>
        </div>
        <hr v-if="setI !== (inputsSets.length - 1)" class="my-2">
    </template>
</template>

<script lang="ts" setup>
import { watch, onUnmounted, nextTick } from 'vue'
import HelpHint from './help-hint.vue'
import Sortable, { SortableEvent } from 'sortablejs'
import { FormInput, FormInputsSet, FormValue } from '@/types.ts'
import FormError from './error.vue'

const sortInstances = [] as Sortable[]

const $props = withDefaults(defineProps<{
    inputsSets: FormInputsSet[]
    help?: Record<string, string>
    hintsEditOpened?: boolean
    formValues: FormValue[]
    errors?: Record<string, string>
    disabled?: boolean
    dragAndDrop?: boolean
    sortableActiveSet?: number
}>(), {
    help: () => ({}),
    errors: () => ({}),
    sortableActiveSet: undefined,
})

const $emit = defineEmits<{
    paramClick: [input: FormInput]
    hintSubmit: [name: string, value: string]
    sortableStart: [blockIndex: number]
    sortableEnd: []
}>()

watch(() => $props.inputsSets, () => {
    if ($props.dragAndDrop) {
        sortableDestroy()
        sortableInit()
    }
}, { flush: 'post', deep: true })

onUnmounted(() => {
    if ($props.dragAndDrop) {
        sortableDestroy()
    }
})

function onParamClick(input: FormInput) {
    $emit('paramClick', input)
}

function onHintSubmit(name: string, value: string) {
    $emit('hintSubmit', name, value)
}

function filterClass(input: FormInput) {
    if (input.name in $props.errors) {
        return 'btn-outline-danger is-invalid'
    }

    if ($props.formValues.map(f => f.name).includes(input.name)) {
        return 'btn-primary'
    }

    return 'btn-secondary'
}

function onSortableEnd() {
    $emit('sortableEnd')
}

function onSortableStart(evt: SortableEvent) {
    $emit('sortableStart', parseInt(evt.to.dataset.block as string))
}

function onSortableRemove(evt: SortableEvent) {
    evt.from.insertBefore(evt.item, evt.clone)
    evt.clone.remove()
}

async function sortableInit() {
    await nextTick()
    $props.inputsSets.forEach((_, i) => {
        const element = document.querySelector(`[data-block="${i}"]`) as HTMLElement
        if (element) {
            sortInstances.push(Sortable.create(element, {
                group: {
                    name: 'report-filters-set-' + i,
                    pull: 'clone',
                },
                sort: false,
                onStart: onSortableStart,
                onEnd: onSortableEnd,
                onRemove: onSortableRemove,
            }))
        }
    })
}

function sortableDestroy() {
    sortInstances.forEach(inst => inst.destroy())
    sortInstances.splice(0)
}
</script>
<style lang="scss">
.form-params {
    .hidden-on-form-params {
        display: none
    }
    .form-params-button-with-validation-on-hover {
        &:hover ~ .invalid-tooltip {
            display: block;
        }
    }
    .is-invalid ~ .invalid-tooltip {
        display: none;
    }
}
</style>
