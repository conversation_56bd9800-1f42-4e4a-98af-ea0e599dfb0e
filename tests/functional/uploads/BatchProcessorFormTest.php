<?php

declare(strict_types=1);

namespace app\tests\functional\uploads;

use app\back\components\BaseCsvUploadRow;
use app\back\components\RuntimeCache;
use app\back\entities\User;
use app\back\entities\UserHistory;
use app\back\entities\UserMetric;
use app\back\entities\UserSpecialInfo;
use app\back\modules\user\statusesUpload\StatusesUploadForm;
use app\back\repositories\Employees;
use app\back\repositories\YhOperators;
use app\back\repositories\UserHistories;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(StatusesUploadForm::class)]
class BatchProcessorFormTest extends TestCase
{
    use CsvUploadTestTrait;

    public function testRequiredValidation(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);

        $this->runCsvValidateAssert('Line 0: Site_id is required', $this->csvData(null, 1, 1, 1), $form);

        $this->runCsvValidateAssert('Line 0: Site_id VV is not integer', $this->csvData('VV', 1, 1, 1), $form);

        $this->runCsvValidateAssert('Line 0: User_id not found: 1-1', $this->csvData(1, 1, 1, 1), $form);

        $this->haveUserRecord(['site_id' => 1, 'user_id' => 1]);
        $this->runCsvValidateAssert('Line 0: Personal_manager 1 is not allowed', $this->csvData(1, 1, 1, 1), $form);

        $this->haveOperatorRecord(['id' => 1]);
        RuntimeCache::clear();
        $this->runCsvValidateAssert('Line 0: Crm_group user special info not found: 1-1', $this->csvData(1, 1, 1, 1), $form);

        $this->haveRecord(UserSpecialInfos::class, ['user_id' => 1, 'site_id' => 1, 'crm_group' => 1]);
        $this->runCsvValidateAssert(null, $this->csvData(1, 1, 1, 1), $form);
    }

    public function testBasicProcessing(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);
        $employeeRepo = $this->container()->get(Employees::class);
        $userId = self::uniqRuntimeId();
        $siteId = self::uniqSiteId();
        $operatorId = self::uniqRuntimeId();
        $crmGroup = UserSpecialInfo::CRM_GROUP_1;
        $this->haveUserRecord(['user_id' => $userId, 'site_id' => $siteId, 'status' => User::STATUS_NEW_VIP]);
        $this->haveOperatorRecord(['id' => $operatorId]);
        $this->haveRecord(UserSpecialInfos::class, ['user_id' => $userId, 'site_id' => $siteId]);

        // all data are inserted
        $data =  $this->csvData($siteId, $userId, $operatorId, $crmGroup);
        $expected = [
            'Updated 1 statuses from 1 rows',
            'Updated 1 personal managers from 1 rows',
            'Updated 1 CRM groups from 1 rows',
            'Updated 1 PM first contact dates from 1 rows',
        ];
        $this->runProcessAssert($expected, $data, $form);
        $this->seeRecord(Users::class, ['user_id' => $userId, 'site_id' => $siteId, 'status' => User::STATUS_PAID, 'active_status' => User::ACTIVE_STATUS_ACTIVE, 'is_manual_status' => true, 'personal_manager' => $operatorId]);
        $this->seeRecord(UserSpecialInfos::class, ['user_id' => $userId, 'site_id' => $siteId, 'crm_group' => $crmGroup]);
        $this->seeRecord(UserMetrics::class, ['user_id' => $userId, 'site_id' => $siteId, 'metric' => UserMetric::M_PM_FIRST_CONTACT_AT[0], UserMetric::M_PM_FIRST_CONTACT_AT[1] => '2022-04-26 10:00:00']);
        $this->seeRecord(UserHistories::class, ['user_id' => $userId, 'site_id' => $siteId, 'employee_id' => $employeeRepo->systemUser()->employee_id, 'source' => UserHistory::SOURCE_UPLOAD]);

        //inserting same data change nothing
        $expected = [
            'Updated 0 statuses from 1 rows',
            'Updated 0 personal managers from 1 rows',
            'Updated 0 CRM groups from 1 rows',
            'Updated 0 PM first contact dates from 1 rows',
        ];
        $this->runProcessAssert($expected, $data, $form);

        //null values doesn't affects stored data
        $data = [['site_id' => $siteId, 'user_id' => $userId]];
        $this->runProcessAssert([], $data, $form);

        //clearing personal_manager
        $expected = [
            'Updated 1 personal managers from 1 rows',
        ];
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'personal_manager' => 0]];
        $this->runProcessAssert($expected, $data, $form);
        $this->seeRecord(Users::class, ['user_id' => $userId, 'site_id' => $siteId, 'personal_manager' => 0]);
    }

    public function testIsManualStatus(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);
        $userId = self::uniqRuntimeId();
        $siteId = self::uniqSiteId();
        $this->haveUserRecord(['user_id' => $userId, 'site_id' => $siteId]);

        //setting is_manual to true
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'is_manual_status' => 1]];
        $this->runProcessAssert(['Updated 1 statuses from 1 rows'], $data, $form);
        $this->seeRecord(Users::class, ['user_id' => $userId, 'site_id' => $siteId, 'is_manual_status' => true]);

        //setting is_manual to false
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'is_manual_status' => 0]];
        $this->runProcessAssert(['Updated 1 statuses from 1 rows'], $data, $form);
        $this->seeRecord(Users::class, ['user_id' => $userId, 'site_id' => $siteId, 'is_manual_status' => false]);
    }

    public function testPmContactedAt(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);
        $userId = self::uniqRuntimeId();
        $siteId = self::uniqSiteId();
        $this->haveUserRecord(['user_id' => $userId, 'site_id' => $siteId]);

        $expected = [
            'Updated 1 PM first contact dates from 1 rows',
        ];

        //set PM first contact at
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'pm_first_contact_at' => '2022-04-26 10:00:00']];
        $this->runProcessAssert($expected, $data, $form);

        //clear PM first contact at
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'pm_first_contact_at' => BaseCsvUploadRow::EMPTY_VALUE]];
        $this->runProcessAssert($expected, $data, $form);
        $this->seeRecord(UserMetrics::class, ['user_id' => $userId, 'site_id' => $siteId, 'metric' => UserMetric::M_PM_FIRST_CONTACT_AT[0], UserMetric::M_PM_FIRST_CONTACT_AT[1] => null]);
    }

    public function testUserHistories(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);
        $userId = self::uniqRuntimeId();
        $siteId = self::uniqSiteId();
        $this->haveUserRecord(['user_id' => $userId, 'site_id' => $siteId]);

        //setting same status doesn't affect history
        $expected = [
            'Updated 0 statuses from 1 rows',
        ];
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'status' => User::STATUS_FREE, 'active_status' => User::ACTIVE_STATUS_ACTIVE]];
        $this->runProcessAssert($expected, $data, $form);

        //history is saved
        $expected = [
            'Updated 1 statuses from 1 rows',
        ];
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'status' => User::STATUS_PAID, 'active_status' => User::ACTIVE_STATUS_AWOL]];
        $this->runProcessAssert($expected, $data, $form);

        $this->seeRecordWithFields(UserHistories::class, ['site_id' => $siteId, 'user_id' => $userId], [
            'changes' => ['status' => User::STATUS_PAID, 'active_status' => User::ACTIVE_STATUS_AWOL],
        ]);
    }

    public function testCrmGroup(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);
        $userId = self::uniqRuntimeId();
        $siteId = self::uniqSiteId();
        $this->haveUserRecord(['user_id' => $userId, 'site_id' => $siteId]);
        $this->haveRecord(UserSpecialInfos::class, ['user_id' => $userId, 'site_id' => $siteId]);

        $expected = [
            'Updated 1 CRM groups from 1 rows',
        ];

        //set crm group
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'crm_group' => UserSpecialInfo::CRM_GROUP_1]];
        $this->runProcessAssert($expected, $data, $form);

        //reset crm group
        $data = [['site_id' => $siteId, 'user_id' => $userId, 'crm_group' => UserSpecialInfo::CRM_GROUP_FAKE_RESET]];
        $this->runProcessAssert($expected, $data, $form);
        $this->seeRecord(UserSpecialInfos::class, ['user_id' => $userId, 'site_id' => $siteId, 'crm_group' => null]);
    }

    /** @see https://jira.syneforge.com/browse/AN-2853 */
    public function testPersonalManagerExtended(): void
    {
        $form = $this->container()->get(StatusesUploadForm::class);

        $userId1 = self::uniqRuntimeId();
        $siteId1 = self::uniqSiteId();
        $userId2 = self::uniqRuntimeId();
        $siteId2 = self::uniqSiteId();
        $userId3 = self::uniqRuntimeId();
        $siteId3 = self::uniqSiteId();

        $operatorId = self::uniqRuntimeId();
        $this->haveUserRecords([
            ['user_id' => $userId1, 'site_id' => $siteId1, 'status' => User::STATUS_FREE],
            ['user_id' => $userId2, 'site_id' => $siteId2, 'status' => User::STATUS_FREE],
            ['user_id' => $userId3, 'site_id' => $siteId3, 'status' => User::STATUS_FREE],
        ]);
        $this->haveOperatorRecord(['id' => $operatorId]);

        $data = [
            ['site_id' => $siteId1, 'user_id' => $userId1, 'is_manual_status' => 1, 'personal_manager' => $operatorId],
            ['site_id' => $siteId2, 'user_id' => $userId2, 'is_manual_status' => 1, 'personal_manager' => null],
            ['site_id' => $siteId3, 'user_id' => $userId3, 'is_manual_status' => 1, 'personal_manager' => 0],
        ];

        $expected = [
            'Updated 3 statuses from 3 rows',
            'Updated 1 personal managers from 3 rows',
        ];

        $this->runProcessAssert($expected, $data, $form);
    }

    private function csvData(mixed $siteId, mixed $userId, mixed $operatorId, mixed $crmGroup): array
    {
        return  [
            [
                'site_id' => $siteId,
                'user_id' => $userId,
                'status' => User::STATUS_PAID,
                'active_status' => User::ACTIVE_STATUS_ACTIVE,
                'is_manual_status' => 1,
                'personal_manager' => $operatorId,
                'crm_group' => $crmGroup,
                'pm_first_contact_at' => '2022-04-26 10:00:00'
            ]
        ];
    }

    private function haveOperatorRecord(array $props): void
    {
        $default = ['id' => self::uniqRuntimeId(), 'email' => '<EMAIL>', 'name' => 'Operator', 'status' => true];
        $this->haveRecord(YhOperators::class, array_merge($default, $props));
    }
}
