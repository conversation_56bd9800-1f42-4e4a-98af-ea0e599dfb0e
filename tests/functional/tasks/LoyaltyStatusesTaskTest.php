<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\entities\Site;
use app\back\repositories\LoyaltyStatuses;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;

class LoyaltyStatusesTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use ApiUnitTrait;

    private const string SMEN_DATA = <<<DATA
id,oldId,brandId,number,title,statusPointsToFinish
3,1,1,1,Be<PERSON><PERSON>,100
21,7,1,2,"Super VIP",50000
24,1,2,1,<PERSON><PERSON><PERSON>,100
42,7,2,2,"Super puper VIP",50000

DATA;

    private const string GI_DATA = <<<DATA
level,status_name,required_experience,composite_status
1,__novice,0,__novice-1
2,__novice,10,__novice-2
3,__novice,30,__novice-3

DATA;

    public function testSmenImport(): void
    {
        $fstBrand = 1;
        $sndBrand = 2;

        $this->runTask('loyalty-statuses', 'S7', $this->debugFile(static::SMEN_DATA));

        $expectedRows = [
            ['site_id' => Site::S7, 'brand_id' => $fstBrand, 'title' => 'Beginner', 'points_to_finish' => '100.00', 'total_points' => '100.00', 'rank' => 1],
            ['site_id' => Site::S7, 'brand_id' => $fstBrand, 'title' => 'Super VIP', 'points_to_finish' => '50000.00', 'total_points' => '50100.00', 'rank' => 2],
            ['site_id' => Site::S7, 'brand_id' => $sndBrand, 'title' => 'Beginner', 'points_to_finish' => '100.00', 'total_points' => '100.00', 'rank' => 1],
            ['site_id' => Site::S7, 'brand_id' => $sndBrand, 'title' => 'Super puper VIP', 'points_to_finish' => '50000.00', 'total_points' => '50100.00', 'rank' => 2],
        ];

        foreach ($expectedRows as $expectedRow) {
            $this->seeRecord(LoyaltyStatuses::class, $expectedRow);
        }

        $this->sendAPI('crm', 'get/dict-loyalty-statuses', [
            'site_id' => ['=' => Site::S7],
            'brand_id' => ['=' => $sndBrand],
        ]);
        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([
            ['id' => 24, 'name' => 'Beginner', 'brand_id' => $sndBrand],
            ['id' => 42, 'name' => 'Super puper VIP', 'brand_id' => $sndBrand],
        ]);
    }

    public function testGiImport(): void
    {
        $this->runTask('loyalty-statuses', 'VV', $this->debugFile(static::GI_DATA));

        $expectedRows = [
            ['site_id' => Site::VV, 'brand_id' => NULL, 'title' => '__novice-1', 'points_to_finish' => NULL, 'total_points' => '0.00', 'rank' => 1],
            ['site_id' => Site::VV, 'brand_id' => NULL, 'title' => '__novice-2', 'points_to_finish' => '10.00', 'total_points' => '10.00', 'rank' => 2],
            ['site_id' => Site::VV, 'brand_id' => NULL, 'title' => '__novice-3', 'points_to_finish' => '20.00', 'total_points' => '30.00', 'rank' => 3],
        ];

        foreach ($expectedRows as $expectedRow) {
            $this->seeRecord(LoyaltyStatuses::class, $expectedRow);
        }

        $this->sendAPI('crm', 'get/dict-loyalty-statuses', [
            'site_id' => ['=' => Site::VV],
        ]);
        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([
            ['id' => 1, 'name' => '__novice-1', 'brand_id' => NULL],
            ['id' => 2, 'name' => '__novice-2', 'brand_id' => NULL],
            ['id' => 3, 'name' => '__novice-3', 'brand_id' => NULL],
        ]);
    }
}
