<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\import;

use app\back\config\tasks\Res;
use app\back\entities\Employee;
use app\back\entities\UserTicket;
use app\back\modules\task\actions\import\UsersTicketsJiraTask;
use app\back\modules\task\requests\JiraBaseRequest;
use app\back\modules\task\requests\JiraSearchIssuesRequest;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketLogs;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersTicketsJiraTask::class)]
class JiraImportTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string JIRA_KEY = 'TEST-1';

    private const array COMMON_FIELDS = [
        'issuetype' => ['name' => JiraBaseRequest::JIRA_ISSUE_TYPE],
        'status' => ['name' => 'Need Approve'],
        'priority' => ['name' => 'Normal'],
        'creator' => ['emailAddress' => Employee::SYSTEM_USER_EMAIL],
        'customfield_17200' => ['value' => 'CV'], // site_id
    ];

    public function testImportIssue(): void
    {
        $this->haveSystemUser();

        $issue = [
            'key' => self::JIRA_KEY,
            'fields' => [
                ...self::COMMON_FIELDS,
                'created' => '2025-01-01T16:00:00.000+0200',
                'updated' => '2025-02-02T16:00:00.000+0200',
            ],
            'changelog' => [],
        ];

        $issue['fields']['customfield_17201'] = null; // user_id
        $this->runImportTask($issue);
        $this->dontSeeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);

        $userId = self::uniqRuntimeId();
        $issue['fields']['customfield_17201'] = $userId;
        $issue['fields']['customfield_17200']['value'] = 'INVALID_SITE_NAME';
        $this->runImportTask($issue);
        $this->dontSeeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);

        $issue['fields']['customfield_17200']['value'] = 'CV';
        $issue['fields']['status']['name'] = 'UNDEFINED STATUS';
        $issue['fields']['customfield_12902'] = null; //invoice_id

        $this->runImportTask($issue);
        $this->seeRecordWithFields(UserTickets::class, ['jira_key' => self::JIRA_KEY], [
            'status' => UserTicket::STATUS_INVALID,
            'invoice_id' => null,
            'created_at' => new \DateTimeImmutable('2025-01-01 14:00:00', new \DateTimeZone('UTC')),
            'updated_at' => new \DateTimeImmutable('2025-02-02 14:00:00', new \DateTimeZone('UTC')),
            'site_id' => 7,
            'user_id' => $userId,
            'source' => UserTicket::SOURCE_JIRA,
        ]);

        $issue['fields']['status']['name'] = 'Need Approve';
        $issue['fields']['updated'] = '2025-03-03T16:00:00.000+0200';
        $this->runImportTask($issue);
        $this->seeRecordWithFields(UserTickets::class, ['jira_key' => self::JIRA_KEY], [
            'status' => UserTicket::STATUS_NEED_APPROVE,
            'updated_at' => new \DateTimeImmutable('2025-03-03 14:00:00', new \DateTimeZone('UTC')),
        ]);
    }

    public function testChangelogImport(): void
    {
        $updatedAt = '2025-02-02T16:00:00.000+0200';
        $issue = [
            'key' => self::JIRA_KEY,
            'fields' => [
                ...self::COMMON_FIELDS,
                'created' => '2025-01-01T16:00:00.000+0200',
                'updated' => $updatedAt,
                'customfield_17201' => self::uniqRuntimeId(), // user_id
            ],
            'changelog' => [
                'histories' => [
                    [
                        'author' => ['emailAddress' => Employee::SYSTEM_USER_EMAIL],
                        'created' => $updatedAt,
                        'items' => [
                            [
                                'field' => 'status',
                                'fromString' => 'Need Approve',
                                'toString' => 'Open',
                            ]
                        ],
                    ],
                ],
            ],
        ];

        $this->runImportTask($issue);
        /** @var UserTicket $ticket */
        $ticket = $this->seeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);
        $this->seeRecords(UserTicketLogs::class, [
            [
                'ticket_id' => $ticket->id,
                'status' => UserTicket::STATUS_NEED_APPROVE,
                'source' => UserTicket::SOURCE_JIRA,
                'created_by' => Employee::SYSTEM_USER_EMAIL,
                'created_at' => $ticket->created_at,
            ],
            [
                'ticket_id' => $ticket->id,
                'status' => UserTicket::STATUS_OPEN,
                'source' => UserTicket::SOURCE_JIRA,
                'created_by' => Employee::SYSTEM_USER_EMAIL,
                'created_at' => $ticket->updated_at,
            ],
        ]);
    }

    private function runImportTask(array $issue): void
    {
        $file = [JiraSearchIssuesRequest::ISSUES_IN_RESPONSE => [$issue], 'total' => 1, 'maxResults' => 1];
        $this->runTask('users-tickets', Res::JIRA, $this->json($file), self::SKIP_EXIT_CODE_CHECK);
    }
}
