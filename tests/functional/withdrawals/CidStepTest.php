<?php

declare(strict_types=1);

namespace app\tests\functional\withdrawals;

use app\back\components\helpers\Money;
use app\back\entities\Site;
use app\back\entities\Withdrawal;
use app\back\modules\finance\withdrawalsAutoProcessing\components\steps\CidStep;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\Withdrawals;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(CidStep::class)]
class CidStepTest extends BaseWithdrawalTestCase
{
    use DbTransactionalUnitTrait;

    public function testCid(): void
    {
        $cid = self::uniqRuntimeId();
        $cid2 = self::uniqRuntimeId();
        $cid3 = self::uniqRuntimeId();

        $user1 = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid]);
        $user2 = $this->haveUserRecord(['site_id' => Site::VV, 'cid' => $cid]);
        $user3 = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid2]);
        $user4 = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid3]);

        $userStat1 = $this->haveUserStatRecordOut($user1);
        $userStat3 = $this->haveUserStatRecordOut($user3);
        $userStat4 = $this->haveUserStatRecordOut($user4);

        $usi = $this->haveRecords(UserSpecialInfos::class, [
            [
                'site_id' => Site::GGB,
                'user_id' => $user1->user_id,
                'dep_lt_usd' => '400.18',
                'wd_lt_usd' => '100.00',
            ],
            [
                'site_id' => Site::VV,
                'user_id' => $user2->user_id,
                'dep_lt_usd' => '50.18',
                'wd_lt_usd' => '0.00',
            ],
            [
                'site_id' => Site::GGB,
                'user_id' => $user3->user_id,
                'dep_lt_usd' => '100',
                'wd_lt_usd' => '0',
            ],
            [
                'site_id' => Site::GGB,
                'user_id' => $user4->user_id,
                'dep_lt_usd' => '520.20',
                'wd_lt_usd' => '0',
            ],
        ]);

        $this->expected->rows_processed = 3;
        $this->expected->rows_finished = 1;

        $this->expected->logs = [
            [
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user1->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user1->user_id CID in-out = " . Money::format($usi[0]->dep_lt_usd + $usi[1]->dep_lt_usd - $usi[0]->wd_lt_usd - $usi[1]->wd_lt_usd) . ", but >= 500 expected",
            ],
            [
                'transaction_id' => $userStat3->transaction_id,
                'user_id' => $user3->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user3->user_id CID in-out = " . Money::format($usi[2]->dep_lt_usd - $usi[2]->wd_lt_usd) . ", but >= 500 expected",
            ],
            [
                'transaction_id' => $userStat4->transaction_id,
                'user_id' => $user4->user_id,
                'decision' => 'success',
                'comment' => "Allow, User: $user4->user_id, Amount: $userStat4->amount_orig $userStat4->currency, Requisite: $userStat4->wallet",
            ],
        ];

        $this->resultProcessRule([
            "Cid(count <= 5; inOutRatio > 3; diff >= 500; currency = USD)",
        ]);

        $this->seeRecord(Withdrawals::class, [
            'site_id' => $user4->site_id,
            'transaction_id' => $userStat4->transaction_id,
            'user_id' => $user4->user_id,
            'status' => Withdrawal::STATUS_NEW,
            'operator_id' => $this->employeeId,
            'decision' => Withdrawal::DECISION_ALLOW,
            'extra' => ['rule_id' => $this->rule->id],
        ]);

        $this->dontSeeRecords(Withdrawals::class, [
            [
                'site_id' => $user1->site_id,
                'transaction_id' => $userStat1->transaction_id,
            ],
            [
                'site_id' => $user3->site_id,
                'transaction_id' => $userStat3->transaction_id,
            ]
        ]);
    }

    public function testSameSiteAndCurrencyCid(): void
    {
        $cid = self::uniqRuntimeId();
        $cid2 = self::uniqRuntimeId();

        $user = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid]);
        $userSameSite = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid]);
        $userOtherSite = $this->haveUserRecord(['site_id' => Site::RC, 'cid' => $cid]);

        $user2 = $this->haveUserRecord(['site_id' => Site::GGB, 'cid' => $cid2]);

        $userStat1 = $this->haveUserStatRecordOut($user);
        $userStat2 = $this->haveUserStatRecordOut($user2);

        $usi = $this->haveRecords(UserSpecialInfos::class, [
            [
                'site_id' => Site::GGB,
                'user_id' => $user->user_id,
                'dep_lt_rub' => '200.18',
                'wd_lt_rub' => '100.00',
            ],
            [
                'site_id' => Site::GGB,
                'user_id' => $userSameSite->user_id,
                'dep_lt_rub' => '400.18',
                'wd_lt_rub' => '50.00',
            ],
            [
                'site_id' => Site::RC,
                'user_id' => $userOtherSite->user_id,
                'dep_lt_rub' => '500.00',
                'wd_lt_rub' => '0.00',
            ],
            [
                'site_id' => Site::GGB,
                'user_id' => $user2->user_id,
                'dep_lt_usd' => '622.28',
                'wd_lt_usd' => '12.18',
            ],
        ]);

        $this->expected->rows_processed = 2;
        $this->expected->rows_finished = 0;
        $this->expected->logs = [
            [
                'transaction_id' => $userStat1->transaction_id,
                'user_id' => $user->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user->user_id CID in-out = " . Money::format($usi[0]->dep_lt_rub + $usi[1]->dep_lt_rub - $usi[0]->wd_lt_rub - $usi[1]->wd_lt_rub) . ", but >= 500 expected",
            ],
            [
                'transaction_id' => $userStat2->transaction_id,
                'user_id' => $user2->user_id,
                'decision' => 'no',
                'comment' => "Not processed, User $user2->user_id CID in-out = " . Money::format($usi[3]->dep_lt_rub - $usi[3]->wd_lt_rub) . ", but >= 500 expected",
            ],
        ];

        $this->resultProcessRule([
            "Cid(diff >= 500; sameSite = 1; currency = RUB)",
        ]);

        $this->dontSeeRecords(Withdrawals::class, [
            [
                'site_id' => $user->site_id,
                'transaction_id' => $userStat1->transaction_id,
            ],
            [
                'site_id' => $user2->site_id,
                'transaction_id' => $userStat2->transaction_id,
            ],
        ]);
    }
}
