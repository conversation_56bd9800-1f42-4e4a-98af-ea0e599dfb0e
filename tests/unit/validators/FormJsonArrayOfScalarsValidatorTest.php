<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\JsonArrayOfScalarsValidator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class FormJsonArrayOfScalarsValidatorTest extends TestCase
{
    #[DataProvider('dataBasic')]
    public function testBasic(mixed $testValue, array $expected): void
    {
        $form = new class {
            use Form;

            #[JsonArrayOfScalarsValidator]
            public array $value;
        };
        $error = $form->validate(['value' => $testValue]);
        $this->assertSame($expected, $error);
    }

    public static function dataBasic(): array
    {
        return [
            ['223.00', ['value' => 'Value not array']],
            [[1 => 1, 2 => 2], ['value' => 'Value is array, but not list']],
            [[1, [2], 3], ['value' => "Value at index 1 isn't scalar"]],
            [[null, 1, 2], ['value' => "Value at index 0 isn't scalar"]],
            [[1, 'a', true], []],
        ];
    }

    #[DataProvider('dataAllowNull')]
    public function testAllowNull(mixed $testValue, array $expected): void
    {
        $form = new class {
            use Form;

            #[JsonArrayOfScalarsValidator(true)]
            public array $value;
        };
        $error = $form->validate(['value' => $testValue]);
        $this->assertSame($expected, $error);
    }

    public static function dataAllowNull(): array
    {
        return [
            [[null, 0,1], []],
            [[1, 2, 3], []],
        ];
    }
}
