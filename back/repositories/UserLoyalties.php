<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserLoyalty;
use <PERSON>isoft\Db\Query\Query;

class UserLoyalties extends BaseRepository
{
    public const string ENTITY_CLASS = UserLoyalty::class;
    public const string TABLE_NAME = 'users_loyalty';
    public const array PRIMARY_KEY = ['user_id', 'site_id'];

    public function getLoyaltyStatus(array $siteUser): ?string
    {
        $data = (new Query($this->db))
            ->select([
                'ls.title',
                'progress' => "ROUND(ul.status_points / NULLIF(ul.status_points_to_finish, 0) * 100)",
                'rank',
                'virtual_status_id',
            ])
            ->from(['ul' => self::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ul.site_id AND u.user_id = ul.user_id')
            ->innerJoin(['ls' => LoyaltyStatuses::TABLE_NAME], 'ls.site_id = ul.site_id AND ls.id = ul.status_id AND ls.brand_id IS NOT DISTINCT FROM u.brand_id')
            ->where([
                'ul.site_id' => $siteUser['site_id'],
                'ul.user_id' => $siteUser['user_id']
            ])
            ->one();

        if ($data === null) {
            return null;
        }

        $status = $data['title'];
        if ($data['virtual_status_id']) {
            $newStatusId = $data['virtual_status_id'] - $data['rank'];
            $status .= ' ' . $newStatusId;
        }

        return $status . ' (' . $data['progress'] . '%)';
    }
}
