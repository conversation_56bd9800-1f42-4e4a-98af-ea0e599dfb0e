<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\WpProgramsTrafficType;

class WpProgramsTrafficTypes extends BaseRepository
{
    use DbDictionaryHelper;

    public const string ENTITY_CLASS = WpProgramsTrafficType::class;
    public const string TABLE_NAME = 'wp_programs_traffic_types';
    public const array PRIMARY_KEY = ['id'];
    public const string NAME_KEY = 'name';
}
