<?php

use app\back\modules\task\components\AnJiraFields;

return [
    'OXR' => [
        'host' => '',
        'appId' => '',
        'clientLogin' => '',
        'clientPassword' => '',
    ],
    'CV' => [
        'host' => $_ENV['MOCK_URL'],
        'authKey' => 'smen-mock-auth-key',
    ],
    'GGB' => [
        'host' => $_ENV['MOCK_URL'],
        'authKey' => 'gi-mock-auth-key',
        'brandId' => 4,
    ],
    'VV' => [
        'host' => $_ENV['MOCK_URL'],
        'authKey' => 'gi-mock-auth-key',
        'brandId' => 5,
    ],
    'GMSD' => [
        'host' => $_ENV['MOCK_URL'],
        'authKey' => 'smen-mock-auth-key-2'
    ],
    'CRM' => [
        'host' => '',
        'client' => '',
        'authKey' => '',
    ],
    'S2P' => [
        'host' => '',
        'authKey' => '',
    ],
    'ML_HUB' => [
        'host' => $_ENV['MOCK_URL'],
        'authKey' => '',
    ],
    'JIRA' => [
        'host' => '',
        'authKey' => '',
        'jiraTimezone' => 'Europe/Kyiv',
        'customFieldsMap' => [
            AnJiraFields::SITE_NAME => 'customfield_17200',
            AnJiraFields::USER_ID => 'customfield_17201',
            AnJiraFields::INVOICE_ID => 'customfield_12902',
            AnJiraFields::PERFORMER => 'customfield_26104',
            AnJiraFields::S2P_ID => 'customfield_12903',
            AnJiraFields::PS_NAME => 'customfield_26107',
            AnJiraFields::AMOUNT_ORIG => 'customfield_11704',
            AnJiraFields::USER_STATUS => 'customfield_18524',
        ],
    ],
];
