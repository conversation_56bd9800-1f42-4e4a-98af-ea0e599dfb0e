<?php

declare(strict_types=1);

namespace app\back\components;

use app\back\components\helpers\Json;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class ResponseCsv extends ResponseTyped
{
    public function __construct(
        protected readonly iterable $data,
        private ?array $cols = null,
        private readonly string $separator = ';',
        private readonly bool $bom = false,
        private readonly string $fileName = 'file.csv',
        private readonly bool $skipEmptyCols = true,
    ) {
        $headers = [
            'Content-Disposition' => HeaderUtils::makeDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, $this->fileName),
            'Content-Type' => 'text/csv; charset=UTF-8',
        ];

        parent::__construct(null, 200, $headers);
    }

    protected function writeFile(\SplFileObject $file): void
    {
        $colsSent = false;

        if ($this->bom) {
            //add BOM to fix UTF-8 in Excel
            $file->fwrite(chr(0xEF) . chr(0xBB) . chr(0xBF));
        }

        foreach ($this->data as $d) {
            if (isset($this->decorator)) {
                $d = call_user_func_array($this->decorator, [&$d]);
            }

            if (!$colsSent) {
                if (!empty($this->cols)) {
                    if ($this->skipEmptyCols) {
                        // Sending filtered by first data row columns
                        $notEmptyCols = [];
                        foreach ($this->cols as $c => $colName) {
                            if (array_key_exists($c, $d) && !str_starts_with($c, '__')) {
                                $notEmptyCols[$c] = $colName;
                            }
                        }
                        $this->cols = $notEmptyCols;
                    }
                } else {
                    $this->cols = array_combine(array_keys($d), array_keys($d));
                }

                $file->fputcsv(array_values($this->cols), $this->separator, '"', '');
                $colsSent = true;
            }

            $row = [];
            foreach ($this->cols as $c => $colName) {
                $v = array_key_exists($c, $d) ? $d[$c] : '';
                if (!(is_scalar($v) || $v instanceof \Stringable)) {
                    $v = Json::encode($v);
                }

                $row[] = $v;
            }

            $file->fputcsv($row, $this->separator, '"', '');

            $this->rowsSent++;
        }
    }
}
