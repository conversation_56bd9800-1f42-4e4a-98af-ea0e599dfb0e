<?php

declare(strict_types=1);

namespace app\back\components;

use JetBrains\PhpStorm\ArrayShape;

readonly class AliasesGuesser
{
    /*
    [
        '"Початок звітності"' => [
            'date' => 'Початок звітності',
            'creative_name' => 'Назва реклами',
            ...
        ],
        '"Дата начала отчетности"' => [
            'date' => 'Дата начала отчетности',
            'creative_name' => 'Название объявления',
            ...
        ],
        ...
        todo: change to ['date' => ['uk' => 'Початок звітності', 'en' => '...'], 'creative_id' => ['uk' => '...', 'en' => '...']]
    ]
    */
    public function __construct(private array $aliasesOptions = [], private bool $checkAllColumns = true)
    {
    }

    /**
     * Look for aliases start and return them for further conversion with content start offset
     * @see AliasesConverter
     */
    #[ArrayShape(["aliases" => "array", "offset" => "int"])]
    public function aliasesAndOffset(string $content): array
    {
        foreach ($this->aliasesOptions as $startWith => $aliases) {
            $offset = mb_strpos($content, $startWith);

            if ($offset === false) {
                continue;
            }

            if ($this->checkAllColumns) {
                foreach ($aliases as $alias) {
                    if (!str_contains($content, $alias)) {
                        break;
                    }
                }
            }

            return compact('aliases', 'offset');
        }

        return ['aliases' => null, 'offset' => 0];
    }
}
