<?php

declare(strict_types=1);

namespace app\back\components;

readonly class PgBytea
{
    private mixed $streamBin;
    private mixed $streamBase64;

    /**
     * @param resource | string $content
     * @param bool $isBase64Encoded
     */
    public function __construct(mixed $content, bool $isBase64Encoded = true)
    {
        $content = match (true) {
            is_resource($content) && get_resource_type($content) === 'stream' => $content,
            is_string($content) => $this->createStream($content),
        };

        if ($isBase64Encoded) {
            $this->streamBase64 = $content;
        } else {
            $this->streamBin = $content;
        }
    }

    private function createStream(string $content): mixed
    {
        $stream = fopen('php://memory', 'rb+');
        fwrite($stream, $content);
        rewind($stream);
        return $stream;
    }

    private function getStreamContents(mixed $stream): string
    {
        rewind($stream);
        return stream_get_contents($stream);
    }

    public function getBase64Data(): string
    {
        return stream_get_contents(
            $this->streamBase64 ??= $this->createStream(base64_encode($this->getStreamContents($this->streamBin)))
        );
    }

    public function getBinData(): string
    {
        return stream_get_contents(
            $this->streamBin ??= $this->createStream(base64_decode($this->getStreamContents($this->streamBase64)))
        );
    }
}
