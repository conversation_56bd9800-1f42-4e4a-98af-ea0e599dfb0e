<?php

declare(strict_types=1);

namespace app\back\modules\back\tasksQueue;

use app\back\repositories\TaskQueues;
use Yiisoft\Db\Connection\ConnectionInterface;

class TasksQueueDeleteForm
{
    use TasksQueueActionTrait;

    public function __construct(private readonly ConnectionInterface $db)
    {
    }

    public function deleteFiltered(): array
    {
        $affected = $this->db->createCommand()->delete(TaskQueues::TABLE_NAME, [
            'id' => $this->idsSubQuery,
        ])->execute();

        return ['affected' => $affected];
    }
}
