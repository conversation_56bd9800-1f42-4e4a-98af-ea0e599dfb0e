<?php

declare(strict_types=1);

namespace app\back\modules\task\components;

final class AnJiraFields
{
    /** Core fields, jira field names are the same at any jira instance */
    public const string TICKET_PROJECT = 'project';
    public const string TICKET_STATUS = 'status';
    public const string TICKET_TYPE = 'type';
    public const string CREATOR = 'creator';
    public const string CREATED = 'created_at';
    public const string UPDATED = 'updated_at';
    public const string PRIORITY = 'priority';
    public const string ATTACHMENT = 'files';
    public const string ASSIGNEE = 'assignee';
    public const string SUMMARY = 'summary';
    public const string HISTORY = 'history';

    public const array JIRA_COMMON_FIELDS = [
        self::TICKET_PROJECT => 'project',
        self::TICKET_TYPE => 'issuetype',
        self::TICKET_STATUS => 'status',
        self::SUMMARY => 'summary',
        self::ASSIGNEE => 'assignee',
        self::PRIORITY => 'priority',
        self::CREATED => 'created',
        self::UPDATED => 'updated',
        self::CREATOR => 'creator',
        self::ATTACHMENT => 'attachment',
    ];

    /** Custom fields, corresponding jira names are defined in host related config */
    public const string SITE_NAME = 'site_name';
    public const string INVOICE_ID = 'invoice_id';
    public const string PERFORMER = 'performer';
    public const string AMOUNT_ORIG = 'amount_orig';
    public const string USER_ID = 'user_id';
    public const string USER_STATUS = 'user_status';
    public const string PS_NAME = 'pay_sys_name';
    public const string S2P_ID = 's2p_id';
}
