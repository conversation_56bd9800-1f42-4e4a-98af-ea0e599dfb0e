<?php

declare(strict_types=1);

namespace app\back\modules\task\components;

use app\back\components\BaseLogHandler;
use app\back\components\Console;
use app\back\components\helpers\Str;
use Monolog\Logger;

class TaskLogHandler extends BaseLogHandler
{
    private bool $errors = false;
    private array $messages = [];

    public function write(array $record): void
    {
        if ($record['level'] >= Logger::ERROR) {
            $this->errors = true;
        }

        if (!isset($this->messages[$record['message']])) {
            $this->messages[$record['message']] = [
                'date' => $record['datetime'],
                'level' => $record['level_name'],
                'times' => 1
            ];
        } else {
            $this->messages[$record['message']]['times']++;
        }
    }

    public function getTaskResult(): array
    {
        $info = [];
        foreach ($this->messages as $messageText => $messageInfo) {
            $message = Console::taskOutputFormat($messageInfo['date'], $messageInfo['level'], $messageText);

            if ($messageInfo['times'] > 1) {
                $message .= " ({$messageInfo['times']} times)";
            }

            $info[] = $message;
        }

        $result = [
            'error' => $this->errors,
            'data' => Str::cleanString(implode("\n", $info)),
        ];

        // Reset local state for further usage
        $this->errors = false;
        $this->messages = [];

        return $result;
    }
}
