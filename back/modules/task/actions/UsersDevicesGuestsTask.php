<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\entities\UserContact;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\Useragents;
use app\back\repositories\UserContacts;

class UsersDevicesGuestsTask extends ImportTask
{
    use UsersDevicesCommonsTrait;
    use TaskWithFromToRequest;

    private DbLargeDictionary $useragentsDict;

    public function __construct(
        private readonly UserContacts $userContactsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        Useragents $useragentsRepo,
    ) {
        $this->useragentsDict = $useragentsRepo->createDictionary();
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();
        $row['type'] = UserContact::TYPE_SUBSCRIPTION;
        $row['source_id'] = UserContact::SOURCE_SUBSCRIPTION;

        if (!empty($row['user_id'])) {
            return false; // Skip not guests
        }

        if (empty($row['value'])) {
            return false;
        }

        $row = $this->fillInfoColumns($row);

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['value'], 'updated_at', SORT_ASC);
        return $repository->batchUpsert($rows, [], '(site_id, type, value) WHERE user_id IS NULL');
    }

    protected function repository(): BaseRepository
    {
        return $this->userContactsRepo;
    }
}
