<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Date;
use app\back\components\Initializable;
use app\back\components\services\GeoIp;
use app\back\components\UserContactAdder;
use app\back\entities\LandingPage;
use app\back\entities\User;
use app\back\entities\UserLogin;
use app\back\entities\UserLyraActivity;
use app\back\modules\events\components\DataQueueTrait;
use app\back\modules\events\rowDatas\UserLoginRow;
use app\back\modules\task\components\UsersLyraActivityTrait;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\AffData;
use app\back\repositories\AffParams;
use app\back\repositories\BaseRepository;
use app\back\repositories\Cities;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\Hosts;
use app\back\repositories\LandingPages;
use app\back\repositories\Refcodes;
use app\back\repositories\SocialNets;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;

class UsersLoginsTask extends ImportTask
{
    use DataQueueTrait;
    use TaskWithFromToRequest;
    use UsersLyraActivityTrait;

    private const array FAIL_REASONS_MAP = [
        // SLOTTY (deprecated)
        'Invalid Password' => UserLogin::FAIL_REASON_BAD_CREDENTIALS,
        'Session Expired' => UserLogin::FAIL_REASON_SESSION_EXPIRED,
        'Account Not Activated' => UserLogin::FAIL_REASON_NOT_ACTIVE,
        'Account Blocked' => UserLogin::FAIL_REASON_BLOCKED,
        'Blocked Country' => UserLogin::FAIL_REASON_BLOCKED_COUNTRY,
        'Blacklisted IP' => UserLogin::FAIL_REASON_BLOCKED_IP,
        'Restriction' => UserLogin::FAIL_REASON_RESTRICTED,
        'Rejected By Fraud' => UserLogin::FAIL_REASON_FRAUD,

        // GI
        'auth.credentials.mismatch' => UserLogin::FAIL_REASON_BAD_CREDENTIALS,
        'auth.credentials.wrong' => UserLogin::FAIL_REASON_BAD_CREDENTIALS,
        'auth.player.locked' => UserLogin::FAIL_REASON_BLOCKED,
        'player.limit.crossed' => UserLogin::FAIL_REASON_LIMIT_REACHED,
        'auth.credentials.blacklisted' => UserLogin::FAIL_REASON_BLACK_LIST_CREDENTIALS,
        'auth.credentials.discredited' => UserLogin::FAIL_REASON_BLACK_LIST_CREDENTIALS,
        'auth.ip.blacklisted' => UserLogin::FAIL_REASON_BLOCKED_IP,
    ];

    private const array FAIL_REASONS_TO_SKIP = [
        UserLogin::FAIL_REASON_BLACK_LIST_CREDENTIALS,
    ];

    private const array METHODS_MAP_GI = [
        // GI auth methods https://jira.syneforge.com/browse/AN-3054
        'login' => UserLogin::METHOD_FORM_LOGIN,
        'social' => UserLogin::METHOD_SOCIAL,
        'token' => UserLogin::METHOD_TOKEN,
        'email' => UserLogin::METHOD_FORM_EMAIL,
        'phone' => UserLogin::METHOD_FORM_PHONE,
        'recovery' => UserLogin::METHOD_PASSWORD_RESET,
        'session' => UserLogin::METHOD_REMEMBER_ME,
        'multi' => UserLogin::METHOD_FORM,
        'trustly' => UserLogin::METHOD_EXTERNAL,
        'apple' => UserLogin::METHOD_APPLE_ID,
        'player' => null,
        'stolen' => null,
    ];

    private const array METHODS_MAP_SMEN = [
        // SMEN auth methods https://jira.syneforge.com/browse/AN-2621
        'remember_me' => UserLogin::METHOD_REMEMBER_ME,
        'admin_api' => UserLogin::METHOD_ADMIN,
        'register_api' => UserLogin::METHOD_AFTER_REG,
        'api' => UserLogin::METHOD_FORM,
        'alogin' => UserLogin::METHOD_TOKEN,
        'ulogin' => UserLogin::METHOD_SOCIAL,
        'form' => UserLogin::METHOD_FORM,
        'reset_password' => UserLogin::METHOD_PASSWORD_RESET,
        'cross_domain' => UserLogin::METHOD_CROSS_DOMAIN,
    ];

    public string $dateFormat = Date::DATETIME_FORMAT_PHP;

    private readonly DbLargeDictionary $landingPagesDict;
    private readonly DbLargeDictionary $useragentsDict;
    private readonly UserContactAdder $contactAdder;

    private int $siteId;

    public function __construct(
        private readonly UserLogins $userLoginsRepo,
        private readonly AffData $affDataRepo,
        private readonly AffParams $affParamsRepo,
        private readonly Refcodes $refcodesRepo,
        LandingPages $landingPagesRepo,
        private readonly SocialNets $socialNetsRepo,
        private readonly Cities $citiesRepo,
        private readonly Users $usersRepo,
        private readonly GeoIp $geoIp,
        private readonly Hosts $hostsRepo,
        Useragents $useragentsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
        $this->landingPagesDict = new DbLargeDictionary($landingPagesRepo::class, $landingPagesRepo->db);
        $this->useragentsDict = $useragentsRepo->createDictionary();
        $this->siteId = $this->siteIdResolver->siteId();
    }

    #[Initializable]
    final public function initUserContactAdder(UserContactAdder $userContactAdder): void
    {
        $userContactAdder->setSkipEvents($this->skipEvents);
        $this->contactAdder = $userContactAdder;
    }

    protected function repository(): UserLogins
    {
        return $this->userLoginsRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, $repository::PRIMARY_KEY);

        if (!$this->skipEvents) {
            $changes = $this->getChanges($repository, $rows);
            $this->sendEvents(UserLoginRow::class, $changes);
        }

        $rowsCount = parent::batchUpsert($repository, $rows);

        if (!$this->skipEvents) {
            $this->updateLyraUsersActivity(UserLyraActivity::SOURCE_LOGINS, $rows);
        }

        return $rowsCount;
    }

    //TODO: remove duplicates (from SMEN) (get more older and success login only)
    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteId;

        if (empty($row['user_id'])) {
            $this->log->debug('Login with empty user. Skipping');
            return false;
        }

        $row['date'] = Date::parseDate($row['date'], $this->dateFormat);

        if (!array_key_exists('login_id', $row)) {
            $row['login_id'] = implode('-', [$row['user_id'], $row['date']]);
        }

        if (!empty($row['fail_reason']) && !is_numeric($row['fail_reason'])) {
            if (array_key_exists($row['fail_reason'], self::FAIL_REASONS_MAP)) {
                $row['fail_reason'] = self::FAIL_REASONS_MAP[$row['fail_reason']];
            } else {
                $this->log->warning("Unresolved fail reason: {$row['fail_reason']}");
                $row['fail_reason'] = null;
            }
        }

        if (in_array($row['fail_reason'] ?? null, self::FAIL_REASONS_TO_SKIP, true)) {
            return false;
        }

        if (!empty($row['useragent'])) {
            $row['useragent_id'] = $this->useragentsDict->getIdByName($row['useragent']);
        }
        unset($row['useragent']);

        if (array_key_exists('gi_method_name', $row)) {
            $row['method'] = self::METHODS_MAP_GI[$row['gi_method_name']];

            if (!empty($row['credentials']) && $row['success']) {
                if ($row['method'] === UserLogin::METHOD_SOCIAL) {
                    $this->updateSocial((int) $row['user_id'], $row['credentials']);
                    $social = $this->socialNetsRepo->getSocialIdKey($row['credentials']);
                    $row['social_id'] = $social['social_id'];
                }
                $this->saveUsersContacts($row);
            }
            unset($row['credentials'], $row['gi_method_name']);
        }

        if (array_key_exists('smen_method_name', $row)) {
            $row['method'] = self::METHODS_MAP_SMEN[$row['smen_method_name']];
            unset($row['smen_method_name']);
        }

        if (!empty($row['code'])) {
            $row['refcode_id'] = $this->refcodesRepo->getIdByCode($row['code']);
        }
        unset($row['code']);

        if (!empty($row['host'])) {
            $row['host_id'] = $this->hostsRepo->getIdByHostAndSite($row['host'], $row['site_id']);
        }
        unset($row['host']);

        if (!empty($row['aff_data'])) {
            $row['aff_data_id'] = $this->affDataRepo->getIdByAffData($row['aff_data']);
            $row['aff_params'] = $this->affParamsRepo->getIds($row['aff_data']);
            $row['sub_data_aff_param_id'] = $this->affParamsRepo->getSubDataAffParamId($row['aff_data']);
        }
        unset($row['aff_data']);

        if (!empty($row['ip']) && is_numeric($row['ip'])) {
            $row['ip'] = long2ip((int)$row['ip']);
        }

        if (!empty($row['ip'])) {
            $row['country'] = $this->geoIp->getCountryCode($row['ip']);
            $row['city_id'] = $this->citiesRepo->getIdByIp($this->geoIp, $row['ip']);
        }

        if (array_key_exists('session_token', $row) && empty($row['session_token'])) {
            unset($row['session_token']);
        }

        if (!empty($row['landing_page'])) {
            $cleanedLp = LandingPage::cleanUrl($row['landing_page']);
            if (!empty($cleanedLp)) {
                $row['lp_id'] = $this->landingPagesDict->getIdByName($cleanedLp, [], 1000);
            }
        }
        unset($row['landing_page']);

        unset($row['count']);

        return true;
    }

    // TODO: move to separate dependent task
    private function saveUsersContacts(array $row): void
    {
        $method = $row['method'];

        if ($method === UserLogin::METHOD_FORM_PHONE && preg_match("/^\+\d+$/", $row['credentials'])) {
            $this->contactAdder->phoneFromLogin($this->siteId, (int)$row['user_id'], $row['credentials']);
        }

        if ($method === UserLogin::METHOD_FORM_EMAIL && preg_match("/^[a-z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-z0-9-]+(?:\.[a-z0-9-]+)*$/i", $row['credentials'])) {
            $this->contactAdder->emailFromLogin($this->siteId, (int)$row['user_id'], $row['credentials']);
        }
    }

    private function updateSocial(int $userId, string $socialIdKey): void
    {
        if (empty($socialIdKey)) {
            return;
        }

        /** @var ?User $user */
        $user = $this->usersRepo->findOne(['site_id' => $this->siteId, 'user_id' => $userId]);

        if ($user === null) {
            return;
        }

        $social = $this->socialNetsRepo->getSocialIdKey($socialIdKey);
        $user->social_id = $social['social_id'];
        $user->social_key = $social['social_key'];

        $this->usersRepo->update($user, ['social_id', 'social_key']);
    }
}
