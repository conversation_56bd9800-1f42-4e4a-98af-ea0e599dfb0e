<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\entities\UserHistory;

trait UsersStatusesUpdateTrait
{
    private function updateUsersStatus(iterable $users, ?int $newStatus = null, ?int $newActiveStatus = null): void
    {
        foreach ($users as $siteUser) {
            $this->totalRows++;
            $affected = $this->usersRepo->updateStatus($siteUser, ['status' => $newStatus, 'active_status' => $newActiveStatus], UserHistory::SOURCE_AUTO);

            if ($affected) {
                $this->affectedRows++;
            }
        }
    }
}
