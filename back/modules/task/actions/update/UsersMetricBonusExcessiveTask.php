<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Date;
use app\back\components\MlHub;
use app\back\components\SecondaryConnection;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\UserGameTokens;
use app\back\repositories\UserMetrics;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class UsersMetricBonusExcessiveTask extends BaseTask
{
    private const int DAYS_FOR_INFER = 30;
    private const int DEP_COUNT_THRESHOLD = 5;

    public bool $dumpRaw = false;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly UserMetrics $userMetricsRepo,
        private readonly MlHub $mlHub,
    ) {
    }

    public function process(): void
    {
        $this->mlHub->setDumRaw($this->dumpRaw);

        $fromInfer = date(Date::DATETIME_FORMAT_PHP, strtotime('-' . (self::DAYS_FOR_INFER - 1) . ' day', $this->fromTime));

        $usersToProcess = new Query($this->db)
            ->select([
                'site_id',
                'user_id',
            ])
            ->from(['usi' => UserSpecialInfos::TABLE_NAME])
            ->where(['AND',
                ['usi.site_id' => $this->siteIdResolver->siteId()],
                ['>=', 'usi.dep_lt_count', self::DEP_COUNT_THRESHOLD],
                ['>=', 'usi.dep_last_at', $this->from],
                ['<', 'usi.dep_last_at', $this->to],
            ]);

        $inDataPayments = new Query($this->db)
            ->select([
                'us.site_id',
                'us.user_id',
                'upd_day' => '(us.updated_at::date)',
                'dep_sum_eur' => 'SUM(us.amount_eur) FILTER (WHERE us.op_id = :op_in)',
                'bonus_count' => 'COUNT(*) FILTER (WHERE us.op_id NOT IN(:op_in))',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->join('NATURAL INNER JOIN', 'u')
            ->where([
                'AND',
                [
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.op_id' => [
                        UserTransaction::OP_IN,
                        UserTransaction::OP_BS_BONUS_MANUAL_ACTIVATION,
                        UserTransaction::OP_BS_BONUS_PROGRESSIVE,
                        UserTransaction::OP_BS_BONUS_SINGLE,
                        UserTransaction::OP_BS_BONUS_MANUAL_ASSIGN,
                        UserTransaction::OP_BS_BONUS_PERSONAL_ASSIGN,
                        UserTransaction::OP_BONUS_BALANCE_PRIZE,
                        UserTransaction::OP_FREE_SPINS,
                        UserTransaction::OP_ADMIN_IN_PRIZE,
                    ],
                ],
                ['>=', 'us.updated_at', $fromInfer],
                ['<', 'us.updated_at', $this->to],
            ])
            ->addParams([
                'op_in' => UserTransaction::OP_IN,
            ])
            ->groupBy(['us.site_id', 'us.user_id', '(us.updated_at::date)']);

        $inDataGames = new Query($this->db)
            ->select([
                'ugt.site_id',
                'ugt.user_id',
                'upd_day' => '(ugt.last_action_at::date)',
                'bet_sum_eur' => 'SUM(ugt.bet_amount_eur)',
            ])
            ->from(['ugt' => UserGameTokens::TABLE_NAME])
            ->join('NATURAL INNER JOIN', 'u')
            ->where([
                'AND',
                ['>=', 'ugt.last_action_at', $fromInfer],
                ['<', 'ugt.last_action_at', $this->to],
            ])
            ->groupBy(['ugt.site_id', 'ugt.user_id', '(ugt.last_action_at::date)']);

        $inData = new Query($this->db)
            ->select([
                'site_id' => 'COALESCE(p.site_id, g.site_id)',
                'user_id' => 'COALESCE(p.user_id, g.user_id)',
                'upd_day' => 'COALESCE(p.upd_day, g.upd_day)',
                'dep_sum_eur' => 'COALESCE(p.dep_sum_eur, 0)',
                'bonus_count' => 'COALESCE(p.bonus_count, 0)',
                'bet_sum_eur' => 'COALESCE(g.bet_sum_eur, 0)',
            ])
            ->withQuery($usersToProcess, 'u')
            ->withQuery($inDataPayments, 'p')
            ->withQuery($inDataGames, 'g')
            ->from('p')
            ->join('NATURAL FULL JOIN', 'g')
            ->all();

        $inDataCount = count($inData);
        if ($inDataCount === 0) {
            return;
        }

        $this->log->debug("Data for ml infer fetched: $inDataCount row(s)");

        $result = $this->mlHub->inferBonusExcessive($inData);

        [$metricId, $metricColumn] = UserMetric::M_BONUS_EXCESSIVE;

        foreach (Arr::batchIterable($result) as $batch) {
            $toUpsert = [];
            foreach ($batch as $row) {
                if ($row['is_bonus_excessive'] === 0) {
                    continue; // Skip users without usefully info
                }
                $toUpsert[] = [
                    'site_id' => $row['site_id'],
                    'user_id' => $row['user_id'],
                    'metric' => $metricId,
                    $metricColumn => $row['is_bonus_excessive'],
                ];
            }

            $this->totalRows += count($toUpsert);
            $this->affectedRows += $this->userMetricsRepo->batchUpsert($toUpsert, ['updated_at' => new \DateTimeImmutable(UserMetric::SQL_NOW_DATETIME)]);
        }
    }
}
