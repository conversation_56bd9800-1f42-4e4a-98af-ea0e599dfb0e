<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\LoyaltyStatuses;

class LoyaltyStatusesTask extends ImportTask
{
    public function __construct(
        private readonly LoyaltyStatuses $loyaltyStatusesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): LoyaltyStatuses
    {
        return $this->loyaltyStatusesRepo;
    }

    protected function getData(): iterable
    {
        $data = Arr::fromIterable($this->createRequest()->finalData());

        if (empty($data)) {
            return [];
        }

        $first = $data[0];
        if (!array_key_exists('total_points', $first) && array_key_exists('points_to_finish', $first)) {
            $rowsByBrandId = Arr::groupBy($data, ['brand_id']);

            foreach ($rowsByBrandId as &$rows) {
                $this->totalFromRequired($rows);
            }
            unset($rows);

            $data = Arr::flatten($rowsByBrandId);
        }

        if (!array_key_exists('points_to_finish', $first) && array_key_exists('total_points', $first)) {
            $this->requiredFromTotal($data);
        }

        return $data;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        return true;
    }

    private function totalFromRequired(array &$rows): void
    {
        usort($rows, static fn($r1, $r2) => $r1['rank'] <=> $r2['rank']);

        $totalPoints = 0;
        foreach ($rows as &$row) {
            $totalPoints += $row['points_to_finish'];
            $row['total_points'] = $totalPoints;
        }
    }

    private function requiredFromTotal(array &$rows): void
    {
        usort($rows, static fn($r1, $r2) => $r1['rank'] <=> $r2['rank']);

        $prevPoints = null;
        foreach ($rows as &$row) {
            if ($prevPoints !== null) {
                $row['points_to_finish'] = $row['total_points'] - $prevPoints;
            }

            $prevPoints = $row['total_points'];
        }
    }
}
