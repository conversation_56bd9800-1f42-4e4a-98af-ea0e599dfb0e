<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Date;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\modules\task\components\AnJiraFields;
use app\back\modules\task\requests\JiraSearchIssuesRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Sites;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTicketLogs;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

/** @property JiraSearchIssuesRequest $request
 * @see https://support.atlassian.com/jira-software-cloud/docs/jql-fields/
 * */
class UsersTicketsJiraTask extends ImportTask
{
    public const array JIRA_STATUSES = [
        'Need Approve' => UserTicket::STATUS_NEED_APPROVE,
        'Open' => UserTicket::STATUS_OPEN,
        'Declined' => UserTicket::STATUS_DECLINED,
        'Description needed' => UserTicket::STATUS_DESCRIPTION_NEEDED,
        'In Progress' => UserTicket::STATUS_IN_PROGRESS,
        'Waiting for Client' => UserTicket::STATUS_WAITING_CLIENT,
        'Not correct request' => UserTicket::STATUS_INCORRECT_REQUEST,
        'Provider Checking' => UserTicket::STATUS_PROVIDER_CHECKING,
        'Provider checking' => UserTicket::STATUS_PROVIDER_CHECKING,
        'Resolved' => UserTicket::STATUS_RESOLVED,
        'Closed' => UserTicket::STATUS_CLOSED,
        'Reopened' => UserTicket::STATUS_REOPENED,
        'Payment received' => UserTicket::STATUS_PAYMENT_RECEIVED,
        'Lost payment' => UserTicket::STATUS_PAYMENT_LOST,
        'Failed payment' => UserTicket::STATUS_PAYMENT_FAILED,
    ];

    public const array JIRA_PRIORITIES = [
        'Blocker' => UserTicket::PRIORITY_BLOCKER,
        'Highest' => UserTicket::PRIORITY_BLOCKER,
        'Critical' => UserTicket::PRIORITY_HIGH,
        'High' => UserTicket::PRIORITY_HIGH,
        'Major' => UserTicket::PRIORITY_HIGH,
        '1' => UserTicket::PRIORITY_HIGH,
        '2' => UserTicket::PRIORITY_HIGH,
        '3' => UserTicket::PRIORITY_HIGH,
        '4' => UserTicket::PRIORITY_HIGH,
        'Medium' => UserTicket::PRIORITY_NORMAL,
        'Normal' => UserTicket::PRIORITY_NORMAL,
        'Trivial' => UserTicket::PRIORITY_NORMAL,
        'Low' => UserTicket::PRIORITY_LOW,
        'Lowest' => UserTicket::PRIORITY_LOW,
        'Minor' => UserTicket::PRIORITY_LOW,
    ];

    private array $ticketsFiles = [];
    private array $ticketsLog = [];

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Sites $sitesRepo,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketFiles $userTicketsFilesRepo,
        private readonly UserTicketLogs $userTicketsLogsRepo,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->userTicketsRepo;
    }

    protected function getData(): iterable
    {
        return $this->createRequest(['from' => $this->from, 'to' => $this->to])->finalData();
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->sitesRepo->getIdByShortName($row[AnJiraFields::SITE_NAME]);
        if ($row['site_id'] === null) {
            $this->log->debug("Unknown site name '{$row[AnJiraFields::SITE_NAME]}' in issue {$row['jira_key']}");
            return false;
        }
        unset($row[AnJiraFields::SITE_NAME]);

        $userId = $row[AnJiraFields::USER_ID] ?? '';
        if (empty($userId) || !is_numeric($userId)) {
            $this->log->debug("User id empty or invalid: {$userId}");
            return false;
        }

        if (array_key_exists($row[AnJiraFields::TICKET_STATUS], self::JIRA_STATUSES)) {
            $row['status'] = self::JIRA_STATUSES[$row[AnJiraFields::TICKET_STATUS]];
        } else {
            $this->log->error("Unknown jira status '{$row[AnJiraFields::TICKET_STATUS]}' in issue {$row['jira_key']}");
            $row['status'] = UserTicket::STATUS_INVALID;
        }

        $row['priority'] = self::JIRA_PRIORITIES[$row[AnJiraFields::PRIORITY]];

        $invoiceId = $row[AnJiraFields::INVOICE_ID] ?? null;
        if (empty($invoiceId) || !preg_match('#[a-zA-Z0-9-]+#', $invoiceId) || strlen($invoiceId) < 3 || strlen($invoiceId) > 36) {
            $this->log->debug(sprintf("Invalid invoice id '%s' in issue %s", $invoiceId ?? '', $row['jira_key']));
            $row['invoice_id'] = null;
        }

        $row['type'] = UserTicket::TYPE_LOST_DEPOSIT;

        $row['source'] = UserTicket::SOURCE_JIRA;

        $this->appendTicketsLogWithConvertedStatuses($row);

        $this->ticketsFiles[$row['jira_key']] = $row[AnJiraFields::ATTACHMENT];

        unset($row[AnJiraFields::ATTACHMENT]);

        return true;
    }

    private function appendTicketsLogWithConvertedStatuses(array $row): void
    {
        $ticketLog = [];
        foreach ($row[AnJiraFields::HISTORY] as $historyRow) {
            $ticketLog[] = [
                ...$historyRow,
                'status' => self::JIRA_STATUSES[$historyRow['status']] ?? UserTicket::STATUS_INVALID,
            ];
        }
        unset($row[AnJiraFields::HISTORY]);

        $this->ticketsLog[$row['jira_key']] = $ticketLog;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        $jiraKeys = array_column($rows, 'jira_key');

        $rows = $this->keepDatesForExistingTickets($rows, $jiraKeys);

        $affected = $this->userTicketsRepo->batchUpsert($rows, ['upserted_at' => new Expression('NOW()')], '(jira_key)');

        $existingTicketsIds = $this->getColumnsFromExistingTickets(['id'], $jiraKeys); // get id of existing + newly inserted tickets

        $logs = $this->setTicketIdFromExistingTickets($this->ticketsLog, $existingTicketsIds);

        $affected += $this->userTicketsLogsRepo->batchUpsert($logs, [], '(ticket_id, status, source, created_at)');

        $files = $this->skipAlreadyDownloadedFiles($this->ticketsFiles);

        $files = $this->setTicketIdFromExistingTickets($files, $existingTicketsIds);

        $affected += $this->userTicketsFilesRepo->batchUpsert($files, [], '(jira_file_id)');

        return $affected;
    }

    private function keepDatesForExistingTickets(array $rows, array $jiraKeys): array
    {
        $existingUpdatedAts = $this->getColumnsFromExistingTickets(['created_at', 'updated_at', 'source'], $jiraKeys);

        foreach ($rows as &$row) {
            if (isset($existingUpdatedAts[$row['jira_key']])) {
                $existingData = $existingUpdatedAts[$row['jira_key']]; //don't overwrite created_at for already existing tickets
                $row['created_at'] = $existingData['created_at'];
                if (strtotime($row['updated_at']->format(Date::DATETIME_FORMAT_PHP)) < strtotime($existingData['updated_at'])) {
                    $row['updated_at'] = $existingData['updated_at']; // don't overwrite updated_at if it newer then imported
                    $row['source'] = $existingData['source'];
                }
            }
        }

        return $rows;
    }

    private function setTicketIdFromExistingTickets(array $toSetId, array $existingTicketsIds): array
    {
        $result = [];
        foreach ($toSetId as $jiraKey => $records) {
            if (!array_key_exists($jiraKey, $existingTicketsIds)) {
                continue;
            }
            foreach ($records as $record) {
                $record['ticket_id'] = $existingTicketsIds[$jiraKey]['id'];
                $result[] = $record;
            }
        }

        return $result;
    }

    private function getColumnsFromExistingTickets(array $columns, array $jiraKeys): array
    {
        $result = new Query($this->db)
            ->select(['jira_key', ...$columns])
            ->from(UserTickets::TABLE_NAME)
            ->where(['jira_key' => $jiraKeys])
            ->all();

        return Arr::index($result, 'jira_key');
    }

    private function skipAlreadyDownloadedFiles(array $files): array
    {
        $downloadedFiles = new Query($this->db)
            ->select(['jira_file_id'])
            ->from(UserTicketFiles::TABLE_NAME)
            ->where(['sync_status' => UserTicketFile::SYNC_SUCCESS])
            ->andWhere(['IS NOT', 'jira_file_id', null])
            ->all();

        return array_filter($files, static fn($file) =>
            !isset($file['jira_file_id']) || !in_array($file['jira_file_id'], $downloadedFiles, true));
    }
}
