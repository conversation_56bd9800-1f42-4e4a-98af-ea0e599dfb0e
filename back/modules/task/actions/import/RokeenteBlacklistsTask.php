<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\entities\BaseEntity;
use app\back\entities\RokeenteBlacklist;
use app\back\modules\task\actions\TaskWithDefaultGetData;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\RokeenteBlacklists;

class RokeenteBlacklistsTask extends ImportTask
{
    use TaskWithDefaultGetData;

    public function __construct(
        private readonly RokeenteBlacklists $rokeenteBlacklistRepo
    ) {
    }

    protected function beforeFind(array &$row): bool
    {
        $row['id'] = RokeenteBlacklist::getBlacklistIdByAlias($row['field']);
        $row['updated_at'] = new \DateTimeImmutable(BaseEntity::SQL_NOW_DATETIME);

        return parent::beforeFind($row);
    }

    protected function repository(): BaseRepository
    {
        return $this->rokeenteBlacklistRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, ['updated_at' => 'NOW()']);
    }
}
