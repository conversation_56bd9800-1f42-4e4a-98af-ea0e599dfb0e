<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\Withdrawal;

class GinWithdrawalRequest extends WithdrawalBaseRequest
{
    public int $brandId;

    protected function createUrl(array $bodyParams): string
    {
        $verb = $this->withdrawal->decision === Withdrawal::DECISION_ALLOW ? 'accept' : 'reject';
        $transactionId = $this->withdrawal->transaction_id;

        $brandId = $this->brandId;
        $url = "api/payment/$transactionId/brand/$brandId/$verb";
        $signature = GinRequest::urlAndSortedParamsHash($url, $this->authKey, $bodyParams);

        return $this->addQueryParams($this->host . '/' . $url, ['signature' => $signature]);
    }
}
