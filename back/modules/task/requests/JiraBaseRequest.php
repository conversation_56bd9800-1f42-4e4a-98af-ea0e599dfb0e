<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\helpers\Date;
use app\back\modules\task\components\AnJiraFields;

abstract class JiraBaseRequest extends BaseRequest
{
    public const string JIRA_ISSUE_TYPE = 'Lost Deposit';
    private const string JIRA_DATETIME_FORMAT = 'Y-m-d\TH:i:s.vO'; /** 2025-03-12T13:48:00.221+0200 **/

    public string $authKey;
    public array $customFieldsMap;
    public string $jiraTimezone;

    protected function dateFromJira(string $dateJira): string
    {
        $date = \DateTimeImmutable::createFromFormat(self::JIRA_DATETIME_FORMAT, $dateJira);
        $date = $date->setTimezone(new \DateTimeZone('UTC'));
        return $date->format(Date::DATETIME_FORMAT_PHP);
    }

    protected function dateToJira(string $dateUtc): string
    {
        $jiraDate = new \DateTimeImmutable($dateUtc, new \DateTimeZone('UTC'));
        $jiraDate = $jiraDate->setTimezone(new \DateTimeZone($this->jiraTimezone));
        return $jiraDate->format(Date::DATETIME_WITHOUT_SECONDS);
    }

    public function getJiraFieldName(string $anField): string
    {
        if (array_key_exists($anField, $this->customFieldsMap)) {
            return $this->customFieldsMap[$anField];
        }

        return AnJiraFields::JIRA_COMMON_FIELDS[$anField];
    }
}
