<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\Initializable;
use app\back\components\parsers\JsonParser;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\modules\task\actions\import\UsersTicketsFilesBaseTask;
use app\back\modules\task\components\AnJiraFields;
use Symfony\Contracts\HttpClient\ResponseInterface;

class JiraSearchIssuesRequest extends JiraBaseRequest
{
    use RequestWithHttpClient;

    use RequestWithParserAndConverter {
        responseToData as innerResponseToData;
    }

    private const int PAGE_SIZE = 1000;
    public const string ISSUES_IN_RESPONSE = 'issues';
    private const string ALIAS_PREFIX = 'fields.';

    private const array ALIAS_SUFFIXES = [
        AnJiraFields::TICKET_STATUS => '.name',
        AnJiraFields::PRIORITY => '.name',
        AnJiraFields::CREATOR => '.emailAddress',
        AnJiraFields::ASSIGNEE => '.emailAddress',
        AnJiraFields::SITE_NAME => '.value',
    ];

    public string $from;
    public string $to;
    public array $fieldsForRequest;

    private array $urlParams = [];
    private int $totalRows = 0;

    #[Initializable]
    final public function initAliasesAndFieldsForRequestAndUrlParams(): void
    {
        $fieldsForRequest = [];
        foreach ($this->fieldsForRequest as $anName) {
            $jira = $this->getJiraFieldName($anName);
            $suffix = self::ALIAS_SUFFIXES[$anName] ?? '';
            $this->aliases[$anName] = self::ALIAS_PREFIX . $jira . $suffix;
            $fieldsForRequest[] = $jira;
        }

        $this->urlParams = [
            ':issueType' => JiraBaseRequest::JIRA_ISSUE_TYPE,
            ':updatedFrom' => $this->dateToJira($this->from),
            ':updatedTo' => $this->dateToJira($this->to),
            ':fields' => implode(',', $fieldsForRequest),
            ':startAt' => 0,
            ':maxResults' => self::PAGE_SIZE,
        ];
    }

    protected function fetchData(): iterable
    {
        do {
            $response = $this->createHttpClient()->get($this->buildUrl($this->urlParams), [
                'auth_bearer' => $this->authKey,
            ]);

            yield from $this->responseToData($response);
        } while ($this->urlParams[':startAt'] < $this->totalRows);
    }

    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $dataPath = self::ISSUES_IN_RESPONSE;
        $dataWithIssues = $this->innerResponseToData($response, $dataPath);
        $this->urlParams[':startAt'] += (int) $dataWithIssues['maxResults'];
        $this->totalRows = $dataWithIssues['total'] ?? 0;
        yield from $this->parseIssues($dataWithIssues[$dataPath]);
    }

    protected function parseIssues(iterable $issues): iterable
    {
        foreach ($issues as $row) {
            if (!isset($row[AnJiraFields::SITE_NAME])) {
                continue;
            }
            $row[AnJiraFields::CREATED] = $this->dateFromJira($row[AnJiraFields::CREATED]);
            $row[AnJiraFields::UPDATED] = $this->dateFromJira($row[AnJiraFields::UPDATED]);
            $row[AnJiraFields::HISTORY] = $this->parseChangelog($row);
            $row[AnJiraFields::ATTACHMENT] = $this->parseAttachments($row);
            yield $row;
        }
    }

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }

    private function parseChangelog(array $row): array
    {
        $firstHistoryRecord = [
            'created_at' => $row[AnJiraFields::CREATED],
            'created_by' => $row[AnJiraFields::CREATOR],
            'source' => UserTicket::SOURCE_JIRA,
            'status' => $row[AnJiraFields::TICKET_STATUS],
        ];

        unset($row[AnJiraFields::CREATOR]);

        if (!array_key_exists(AnJiraFields::HISTORY, $row)) {
            return [$firstHistoryRecord];
        }

        foreach ($row[AnJiraFields::HISTORY] as $items) {
            foreach ($items['items'] as $item) {
                if ($item['field'] === 'status' && array_key_exists('fromString', $item)) {
                    $firstHistoryRecord['status'] = $item['fromString'];
                    break 2;
                }
            }
        }

        $changelog = [$firstHistoryRecord];
        foreach ($row[AnJiraFields::HISTORY] as $items) {
            foreach ($items['items'] as $item) {
                if ($item['field'] !== 'status' || !array_key_exists('toString', $item)) {
                    continue;
                }

                $changelog[] = [
                    'created_at' => $this->dateFromJira($items['created']),
                    'created_by' => $items['author']['emailAddress'],
                    'source' => UserTicket::SOURCE_JIRA,
                    'status' => $item['toString'],
                ];
            }
        }

        return $changelog;
    }

    private function parseAttachments(array $row): array
    {
        $result = [];

        if (!array_key_exists(AnJiraFields::ATTACHMENT, $row)) {
            return $result;
        }

        foreach ($row[AnJiraFields::ATTACHMENT] as $attachment) {
            if (!array_key_exists($attachment['mimeType'], UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED)) {
                $this->log->debug("Forbidden mime type {$attachment['mimeType']} in issue {$row['jira_key']}");
                continue;
            }

            $result[] = [
                'jira_file_id' => $attachment['id'],
                'extension' => UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED[$attachment['mimeType']],
                'original_name' => urldecode($attachment['filename']),
                'created_at' => $this->dateFromJira($attachment['created']),
                'sync_status' => UserTicketFile::SYNC_TO_DOWNLOAD,
                'source' => UserTicket::SOURCE_JIRA,
            ];
        }

        return $result;
    }
}
