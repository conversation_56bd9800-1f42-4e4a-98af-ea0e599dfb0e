<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\exceptions\InvalidRemoteException;
use app\back\components\helpers\Json;
use app\back\components\parsers\JsonParser;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

abstract class JiraPostRequest extends JiraBaseRequest
{
    use RequestWithHttpClient {
        handleStatusCode as innerHandleStatusCode;
    }
    use RequestWithParserAndConverter;

    abstract protected function requestParams(): array;

    protected function fetchData(): iterable
    {
        $response = $this->createHttpClient()->post($this->buildUrl(), $this->requestParams(), [
            'auth_bearer' => $this->authKey,
        ], true);

        return $this->responseToData($response);
    }

    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $this->handleStatusCode($response);
        $content = $response->getContent(false);
        return match (true) {
            empty($content) => [],
            json_validate($content) => $this->parseAndConvert($content),
            default => $content,
        };
    }

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }

    protected function handleStatusCode(ResponseInterface $response): void
    {
        match ($response->getStatusCode()) {
            Response::HTTP_CREATED, Response::HTTP_NO_CONTENT => null,
            Response::HTTP_BAD_REQUEST => throw new InvalidRemoteException($this->parseErrors($response), Response::HTTP_BAD_REQUEST),
            default => $this->innerHandleStatusCode($response),
        };
    }

    protected function parseErrors(ResponseInterface $response): string
    {
        $content = Json::decode($response->getContent(false));

        $messages = [];

        if (!empty($content['errorMessages']) && is_array($content['errorMessages'])) {
            $messages[] = implode("\n", $content['errorMessages']);
        }

        if (!empty($content['errors']) && is_array($content['errors'])) {
            foreach ($content['errors'] as $field => $error) {
                $messages[] = "$field: $error";
            }
        }

        return $messages ? implode("\n", $messages) : 'error message is empty';
    }
}
