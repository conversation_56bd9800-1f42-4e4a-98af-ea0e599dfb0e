<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\UserTicket;

class JiraTicketUpdateStatusRequest extends JiraPostRequest
{
    /** Jira API accepts only transition IDs (not names), these IDs are the same on both servers */
    private const array TRANSITIONS_MAP = [
        UserTicket::STATUS_OPEN => 101,
        UserTicket::STATUS_DECLINED => 91,
    ];

    public string $jiraKey;
    public int $analyticStatus;

    protected function buildUrl(array $params = []): string
    {
        return parent::buildUrl([':jiraKey' => $this->jiraKey]);
    }

    protected function requestParams(): array
    {
        return ['transition' => ['id' => self::TRANSITIONS_MAP[$this->analyticStatus]]];
    }
}
