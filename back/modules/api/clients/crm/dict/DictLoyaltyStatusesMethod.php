<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\dict;

use app\back\components\validators\IntValidator;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamSiteId;
use app\back\repositories\LoyaltyStatuses;
use Yiisoft\Db\Query\Query;

class DictLoyaltyStatusesMethod extends ApiGetMethod
{
    use ApiParamSiteId;

    #[IntValidator]
    #[Operators(Operators::EQ)]
    protected array $brand_id = [];

    public function run(): iterable
    {
        $request = $this->createRequest();

        $query = (new Query($this->db))
            ->select([
                'id',
                'name' => 'title',
                'brand_id',
            ])
            ->from(LoyaltyStatuses::TABLE_NAME);

        $request->map([
            'brand_id' => 'brand_id'
        ]);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
