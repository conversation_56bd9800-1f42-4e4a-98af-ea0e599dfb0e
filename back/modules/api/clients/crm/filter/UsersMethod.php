<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\crm\filter;

use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\Date;
use app\back\components\Initializable;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\enums\VipaffAffSource;
use app\back\entities\TrafficSource;
use app\back\entities\User;
use app\back\entities\UserKyc;
use app\back\entities\UserWallet;
use app\back\modules\api\components\Operators;
use app\back\repositories\Refcodes;
use app\back\repositories\UserKycs;
use app\back\repositories\Users;
use app\back\repositories\UserWallets;
use app\back\repositories\VipaffRefcodes;
use Yiisoft\Db\Query\Query;

class UsersMethod extends BaseCrmFilterMethod
{
    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[MoneyValidator]
    protected array $balance_usd = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[MoneyValidator]
    protected array $balance_orig = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[StringValidator(3, 5)]
    protected array $currency = [];

    #[Operators(Operators::COMPARISONS)]
    #[DateTimeValidator]
    protected array $registered_at = [];

    #[Operators(Operators::COMPARISONS)]
    #[DateTimeValidator]
    protected array $email_confirmed_at = [];

    #[Operators(Operators::EQ_IN)]
    #[BooleanValidator]
    protected array $is_email_confirmed = [];

    #[Operators(Operators::EQ_IN)]
    #[BooleanValidator]
    protected array $is_phone_confirmed = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[StringValidator(2, 2)]
    protected array $country = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[StringValidator(2, 5)]
    protected array $locale = [];

    #[Operators(Operators::PREFIX_IN, Operators::PREFIX_NOT_IN)]
    #[StringValidator(2, 250)]
    protected array $ref_code = [];

    #[Operators(Operators::BETWEEN)]
    #[DateValidator]
    protected array $birthday = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IdValidator]
    protected array $social_net_id = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator(TrafficSource::NAMES)]
    protected array $traffic_source = [];

    #[Operators(Operators::EQ_IN)]
    #[BooleanValidator]
    protected array $is_blocked = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[BooleanValidator]
    protected array $is_toxic = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    protected array $status = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    protected array $active_status = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[BooleanValidator]
    protected array $is_rm = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[BooleanValidator]
    protected array $is_bbl = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[BooleanValidator]
    protected array $is_kyc_confirmed = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IdValidator]
    protected array $personal_manager_id = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator(User::TYPES_OF_GAMBLING)]
    protected array $priority_type_of_gambling = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IdValidator]
    protected array $brand_id = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[StringInArrayValidator([VipaffAffSource::class, 'values'], true)]
    protected array $vipaff_aff_source = [];

    #[Operators(Operators::COMPARISONS, Operators::EQ)]
    #[IntValidator]
    protected array $age = [];

    #[Operators(Operators::EQ_IN)]
    #[StringValidator(1, 36)]
    protected array $social_key = [];

    #[Operators(Operators::EQ)]
    #[StringInArrayValidator(['M', 'F'], true)]
    public array $gender = [];

    #[Operators(Operators::COMPARISONS)]
    #[DateTimeValidator]
    protected array $status_updated_at = [];

    private readonly UserWallets $userWalletsRepo;

    #[Initializable]
    final public function init(UserWallets $userWalletsRepo): void
    {
        $this->userWalletsRepo = $userWalletsRepo;
    }

    protected function getMainQuery(): Query
    {
        $request = $this->createRequest();

        $kycVerified = UserKyc::KYC_VERIFIED;

        $query = (new Query($this->db))
            ->select([
                'u.user_id',
            ])
            ->from(['u' => Users::TABLE_NAME]);

        // Join ref codes only if need
        if ($request->existParam('traffic_source') || $request->existParam('ref_code')) {
            $query->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id');
        }

        // Join VipAff refcodes only if need
        if ($request->existParam('vipaff_aff_source')) {
            $query->leftJoin(['vr' => VipaffRefcodes::TABLE_NAME], 'vr.refcode_id = u.refcode_id');
        }

        if ($request->existParam('is_kyc_confirmed')) {
            $query->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id');
        }

        if ($request->existParam('birthday')) {
            $birthday = $request->getParam('birthday', null, true, ['BETWEEN']);

            if (!is_array($birthday) || count($birthday) !== 2) {
                throw new InvalidException('Birthday params must be array with size 2');
            }

            [$birthdayFrom, $birthdayTo] = $birthday;

            $query->andWhere(['u.birthday' => Date::getBirthdays($birthdayFrom, $birthdayTo)]);
        }

        if ($request->existParam('gender')) {
            $query->andWhere(['u.gender' => $request->getParam('gender', null, true) === 'M' ? 1 : 0]);
        }

        $request->map([
            'site_id' => 'u.site_id',
            'balance_usd' => 'uw.balance_usd',
            'balance_orig' => 'uw.balance_orig',
            'currency' => 'uw.currency',
            'registered_at' => 'u.date',
            'is_email_confirmed' => 'u.email_confirm',
            'email_confirmed_at' => 'u.email_confirmed_at',
            'is_phone_confirmed' => 'u.phone_confirm',
            'country' => 'u.country',
            'locale' => 'u.locale',
            'ref_code' => 'r.code',
            'traffic_source' => 'r.ts_id',
            'social_net_id' => 'u.social_id',
            'social_key' => 'u.social_key',
            'birthday' => 'u.birthday',
            'is_blocked' => 'u.is_blocked',
            'is_toxic' => 'u.is_toxic',
            'status' => 'u.status',
            'active_status' => 'u.active_status',
            'is_rm' => 'u.is_rm',
            'is_kyc_confirmed' => "COALESCE((kyc.kyc_status = $kycVerified), false)",
            'is_bbl' => '(u.bbl_status != ' . User::BONUS_BL_STATUS_NO . ')',
            'personal_manager_id' => 'u.personal_manager',
            'priority_type_of_gambling' => 'u.priority_type_of_gambling',
            'brand_id' => 'u.brand_id',
            'vipaff_aff_source' => 'vr.aff_source',
            'age' => "DATE_PART('year', AGE(u.birthday))",
            'gender' => 'u.gender',
            'status_updated_at' => 'u.status_updated_at',
        ], true);

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        $query
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->andWhere($request->buildWhere());

        return $query;
    }

    protected function getMainQueryAlias(): string
    {
        return 'u';
    }
}
