<?php

declare(strict_types=1);

namespace app\back\modules\user\statusesUpload;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\BaseCsvUploadForm;
use app\back\components\BaseCsvUploadRow;
use app\back\components\helpers\Arr;
use app\back\components\Permission;
use app\back\components\ResponseCsv;
use app\back\entities\User;
use app\back\entities\UserSpecialInfo;
use app\back\modules\user\statusesUpload\handlers\UserCrmGroupHandler;
use app\back\modules\user\statusesUpload\handlers\UserPersonalManagerHandler;
use app\back\modules\user\statusesUpload\handlers\UserPmFirstContactHandler;
use app\back\modules\user\statusesUpload\handlers\UserStatusesHandler;
use app\back\repositories\YhOperators;
use app\back\repositories\Users;

class StatusesUploadForm extends BaseCsvUploadForm
{
    public function __construct(
        private readonly UserStatusesHandler $userStatusesHandler,
        private readonly UserPersonalManagerHandler $userPersonalManagerHandler,
        private readonly UserCrmGroupHandler $userCrmGroupHandler,
        private readonly UserPmFirstContactHandler $userPmFirstContactHandler,
        private readonly StatusesUploadRow $batchProcessorRow,
        private readonly AllowedLists $allowedLists,
        private readonly YhOperators $operatorsRepo,
        protected readonly BaseAuthAccess $auth,
    ) {
    }

    protected static function uniqueColumns(): array
    {
        return Users::PRIMARY_KEY;
    }

    public static function csvColumnsMap(): array
    {
        return [
            'site_id' => 'Project Id',
            'user_id' => 'User Id',
            'status' => 'Status Id',
            'active_status' => 'Active Status Id',
            'is_manual_status' => 'Is Manual',
            'personal_manager' => 'Personal Manager Id',
            'crm_group' => 'Crm Group',
            'pm_first_contact_at' => 'PM first contact at',
        ];
    }

    public function composeResponse(): array
    {
        $form = $this->response();
        $sites = Arr::assocToIdName($this->allowedLists->sites());
        $statuses = Arr::assocToIdName(User::actualStatuses());
        $activeStatuses = Arr::assocToIdName(User::ACTIVE_STATUSES);
        $crmGroups = Arr::columnToIdName(UserSpecialInfo::CRM_GROUPS);
        $columns = implode(static::DELIMITER, static::csvColumnsMap());
        $delimiter = static::DELIMITER;
        $permissions = [
            'canChangeUserPm' => $this->auth->can(Permission::PERM_USER_PM),
            'canChangeUserStatus' => $this->auth->can(Permission::PERM_USER_STATUS),
            'canChangeCrmGroup' => $this->auth->can(Permission::PERM_CHANGE_CRM_GROUP),
        ];

        return compact('form', 'delimiter', 'columns', 'sites', 'statuses', 'activeStatuses', 'crmGroups', 'permissions');
    }

    public function exampleFile(): ResponseCsv
    {
        return new ResponseCsv(
            $this->example(),
            static::csvColumnsMap(),
            bom: true,
            fileName: 'analytics_statuses_example.csv',
        );
    }

    public function personalManagersFile(): ResponseCsv
    {
        return new ResponseCsv(
            $this->operatorsRepo->getIdNameDict(),
            [
                'id' => 'Id',
                'name' => 'Name',
            ],
            separator: $this->auth->employee()->getSettingsObject()->getSeparator(),
            bom: true,
            fileName: 'analytics_personal_managers.csv',
        );
    }

    protected function dictionaries(): array
    {
        return [];
    }

    protected function example(): array
    {
        return [
            [
                'site_id' => 1,
                'user_id' => 1,
                'status' => 1,
                'active_status' => 1,
                'is_manual_status' => 1,
                'personal_manager' => 1,
                'crm_group' => 1,
                'pm_first_contact_at' => '2022-04-26 10:00:00',
            ]
        ];
    }

    protected function rowHandlers(): array
    {
        return [
            $this->userStatusesHandler,
            $this->userPersonalManagerHandler,
            $this->userCrmGroupHandler,
            $this->userPmFirstContactHandler,
        ];
    }

    protected function rowForm(): BaseCsvUploadRow
    {
        return $this->batchProcessorRow;
    }

    /** @param self $form */
    public static function validateInChildren(array $rows, $form, array $context): ?string
    {
        $permissionsMap = [
            'status' => Permission::PERM_USER_STATUS,
            'active_status' => Permission::PERM_USER_STATUS,
            'is_manual_status' => Permission::PERM_USER_STATUS,
            'personal_manager' => Permission::PERM_USER_PM,
            'crm_group' => Permission::PERM_CHANGE_CRM_GROUP,
        ];

        foreach ($permissionsMap as $propName => $permission) {
            if (!empty(array_column($rows, $propName)) && !$form->auth->can($permission)) {
                return 'Absent permission to modify ' . $form->alias($propName);
            }
        }

        return null;
    }
}
