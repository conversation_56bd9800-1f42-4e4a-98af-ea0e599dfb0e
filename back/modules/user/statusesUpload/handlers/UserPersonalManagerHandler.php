<?php

declare(strict_types=1);

namespace app\back\modules\user\statusesUpload\handlers;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\SessionMessages;
use app\back\entities\UserHistory;
use app\back\repositories\Users;

readonly class UserPersonalManagerHandler
{
    public function __construct(
        private Users $usersRepo,
        private BaseAuthAccess $auth,
        private SessionMessages $sessionMessages,
    ) {
    }

    public function __invoke(array $rows): void
    {
        $notEmptyRows = array_filter($rows, static fn ($v) => Arr::anyKeyExists($v, ['personal_manager']));

        if (empty($notEmptyRows)) {
            return;
        }

        $rowsToUpsert = array_map(static fn ($v) => Arr::leaveOnlyKeys($v, ['site_id', 'user_id', 'personal_manager']), $notEmptyRows);

        $updated = 0;
        $tr = $this->usersRepo->db->beginTransaction();
        foreach ($rowsToUpsert as $row) {
            $updated += $this->usersRepo->updatePm(Arr::leaveOnlyKeys($row, ['site_id', 'user_id']), UserHistory::SOURCE_UPLOAD, $row['personal_manager'], $this->auth->employeeId());
        }
        $tr->commit();

        $this->sessionMessages->success(sprintf("Updated %d personal managers from %d rows", $updated, count($rows)));
    }
}
