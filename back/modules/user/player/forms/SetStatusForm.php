<?php

declare(strict_types=1);

namespace app\back\modules\user\player\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\User;
use app\back\entities\UserHistory;
use app\back\repositories\Users;

class SetStatusForm extends BasePlayerForm
{
    use FormGrid;

    #[StringInArrayValidator(['status', 'activeStatus', 'isManual'], true)]
    public string $column;
    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    public ?int $status = null;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public ?int $activeStatus = null;
    #[BooleanValidator]
    public ?bool $isManual = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly Users $usersRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->listCell(12, 'status', 'Status', [
                    'list' => Arr::assocToIdName(User::actualStatuses()),
                    'multiple' => false,
                ]),
            ],
            [
                $this->listCell(8, 'activeStatus', 'Active', [
                    'list' => Arr::assocToIdName(User::ACTIVE_STATUSES),
                    'multiple' => false,
                ]),
                $this->selectBooleanCell(4, 'isManual', 'Is manual', [
                    'multiple' => false,
                ]),
            ],
        ];
    }

    public function updateStatus(): void
    {
        $siteUser = [
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
        ];

        $toUpdate = [
            $this->column => $this->{$this->column},
        ];

        $this->usersRepo->updateStatus($siteUser, $toUpdate, UserHistory::SOURCE_PLAYER, null, $this->auth->employeeId());
    }
}
