<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\customer;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\ForbiddenException;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\validators\IntValidator;
use app\back\entities\CustomerAttribute;
use app\back\entities\CustomerUser;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\CustomerAttributes;
use app\back\repositories\CustomerUsers;
use app\back\repositories\Employees;
use app\back\repositories\Sites;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class CustomerForm extends BasePlayerForm
{
    use RichTable {
        validateAndResponse as gridValidateAndResponse;
    }

    private const string ATTR_GROUP_USER_LINK = 'user-link';

    private const array PRIORITY_MAP = [
        CustomerAttribute::TYPE_NAME,
        CustomerAttribute::TYPE_JOB,
        CustomerAttribute::TYPE_SOCIAL_LINK,
        CustomerAttribute::TYPE_ADDRESS,
        CustomerAttribute::TYPE_DESCRIPTION,
        CustomerAttribute::TYPE_OFFLINE,
    ];

    private const array TABLE_ATTRIBUTES = [
        CustomerAttribute::TYPE_BONUS => 'Bonuses',
        CustomerAttribute::TYPE_PROPOSITION => 'Propositions',
        CustomerAttribute::TYPE_COMMENT => 'Comments',
    ];

    private const array ATTRIBUTE_SELECT_FIELDS = [
        'ca.id',
        'ca.type_id',
        'ca.value',
        'ca.created_at',
        'ca.created_by',
    ];

    #[IntValidator]
    private int $typeId = CustomerAttribute::TYPE_BONUS;

    private CustomerUser $customerUser;

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly CustomerUsers $customerUsersRepo,
        private readonly Employees $employeesRepo,
        private readonly Sites $sitesRepo,
        private readonly FileStorage $storage,
    ) {
        parent::__construct($allowedLists, $db, $auth);
        $this->pageSize = 50;
    }

    public function validateAndResponse(array $requestData): array
    {
        if (!$this->auth->canViewCustomer()) {
            throw new ForbiddenException("You can't view customer info");
        }

        $this->validateOrException($requestData);

        /** @var CustomerUser $customerUser */
        $customerUser = $this->customerUsersRepo->findOne(['site_id' => $this->siteId, 'user_id' => $this->userId]);
        if ($customerUser === null) {
            return [];
        }

        $this->customerUser = $customerUser;


        return $this->gridValidateAndResponse($requestData);
    }

    protected function additionalResponse(): array
    {
        return [
            'customerId' => $this->customerUser->customer_id,
            'attributeTypes' => $this->getProfileTypes(),
            'attributes' => $this->getProfileAttributes(),
            'linkedUsers' => $this->getLinkedUsers(),
        ];
    }

    protected function blocks(): array
    {
        $list = (new Query($this->db))
            ->select([
                'a.id',
                'a.name',
                'count' => 'COUNT(ca.id)',
            ])
            ->from(Db::valuesTable($this->db, Arr::assocToIdName(self::TABLE_ATTRIBUTES), ['id' => 'int', 'name' => 'text'], 'a'))
            ->leftJoin(['ca' => CustomerAttributes::TABLE_NAME], [
                'AND',
                'ca.type_id = a.id',
                ['ca.customer_id' => $this->customerUser->customer_id],
                ['ca.customer_user_id' => [$this->customerUser->id, null]],
            ])
            ->groupBy('a.id, a.name')
            ->orderBy(new Expression(Db::buildCaseCondition($this->db, ['a.id' => array_values(array_keys(self::TABLE_ATTRIBUTES))])))
            ->all();

        return [
            [
                $this->slotCell(12, 'createAttribute', '', ['hideLabel' => true]),
                $this->listCell(12, 'typeId', '', ['list' => $list, 'multiple' => false, 'size' => 'sm', 'hideLabel' => true]),
            ]
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => null, 'code' => 'value', 'slotName' => 'attrValue', 'align' => 'start'],
            ['name' => null, 'code' => 'created_at', 'slotName' => 'createdAt', 'align' => 'start'],
            ['name' => null, 'code' => 'created_by', 'slotName' => 'createdBy', 'align' => 'start'],
        ];
    }

    public function total(): int
    {
        return $this->dataQuery(self::TABLE_ATTRIBUTES, $this->typeId)->select('COUNT(ca.type_id)')->scalar();
    }

    protected function data(): array
    {
        $attributes = $this->dataQuery(self::TABLE_ATTRIBUTES, $this->typeId)
            ->select(self::ATTRIBUTE_SELECT_FIELDS)
            ->orderBy($this->getOrderMap() ?: ['created_at' => SORT_DESC])
            ->offset($this->getOffset())
            ->limit($this->getLimit())
            ->all();

        return $this->processAttributes($attributes);
    }

    private function dataQuery(array $typeIdNames, ?int $typeId = null): Query
    {
        return (new Query($this->db))
            ->from(Db::valuesTable($this->db, Arr::assocToIdName($typeIdNames), ['id' => 'int', 'name' => 'text'], 'a'))
            ->leftJoin(['ca' => CustomerAttributes::TABLE_NAME], "ca.type_id = a.id")
            ->where([
                'ca.customer_id' => $this->customerUser->customer_id,
                'ca.customer_user_id' => [$this->customerUser->id, null],
            ])
            ->andFilterWhere(['a.id' => $typeId]);
    }

    private function getProfileTypeIds(): array
    {
        $forbidden = $this->auth->canViewAddress() ? [] : [CustomerAttribute::TYPE_ADDRESS];
        return array_diff_key(CustomerAttribute::TYPES, self::TABLE_ATTRIBUTES, $forbidden);
    }

    private function processAttributes(array $attributes): array
    {
        foreach ($attributes as &$attribute) {
            $attribute['created_at'] = date('Y-m-d', strtotime($attribute['created_at']));
            $attribute['created_by'] = Str::emailToLdap($this->employeesRepo->getNameById($attribute['created_by']));
            if ($attribute['type_id'] === CustomerAttribute::TYPE_IMAGE) {
                $attribute['value'] = $this->storage->getPublicUrlByKey("customers/{$this->customerUser->customer_id}/{$attribute['value']}");
            }
        }
        return $attributes;
    }

    private function getProfileTypes(): array
    {
        $profileTypes = [];
        foreach ($this->getProfileTypeIds() as $type_id => $name) {
            $unique = in_array($type_id, CustomerAttribute::UNIQUE_TYPES, true);
            $group = match ($type_id) {
                CustomerAttribute::TYPE_DESCRIPTION,
                CustomerAttribute::TYPE_OFFLINE => 'description',
                CustomerAttribute::TYPE_IMAGE => 'image',
                default => 'profile',
            };

            $res = compact('type_id', 'name', 'group', 'unique');

            $priority = array_search($type_id, self::PRIORITY_MAP, true);
            if ($priority !== false) {
                $res['priority'] = $priority;
            }
            $profileTypes[] = $res;
        }

        return $profileTypes;
    }

    private function getLinkedUsers(): array
    {
        $linkedUsers = (new Query($this->db))
            ->select([
                'site_id',
                'user_id',
                'created_at' => '(created_at::date)',
                'created_by',
            ])
            ->from(CustomerUsers::TABLE_NAME)
            ->where([
                'AND',
                ['customer_id' => $this->customerUser->customer_id],
                ['!=', 'user_id', $this->customerUser->user_id],
            ])
            ->all();

        foreach ($linkedUsers as &$user) {
            $user['created_by'] = $this->employeesRepo->getNameById($user['created_by']);
            $user['site_name'] = $this->sitesRepo->getShortNameById($user['site_id']);
            $user['group'] = static::ATTR_GROUP_USER_LINK;
        }

        return $linkedUsers;
    }

    private function getProfileAttributes(): array
    {
        $attributes = $this->dataQuery($this->getProfileTypeIds())
            ->select(self::ATTRIBUTE_SELECT_FIELDS)
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        return $this->processAttributes($attributes);
    }
}
