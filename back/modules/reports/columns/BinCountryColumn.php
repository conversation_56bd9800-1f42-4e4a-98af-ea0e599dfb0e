<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use Yiisoft\Db\Connection\ConnectionInterface;

class BinCountryColumn extends BaseColumn implements Selected
{
    public string $column = 'country';

    public string $title = 'Card issuer country';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
