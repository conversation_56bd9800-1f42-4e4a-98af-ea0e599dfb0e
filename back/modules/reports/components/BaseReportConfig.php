<?php

declare(strict_types=1);

namespace app\back\modules\reports\components;

use app\back\components\Container;
use app\back\components\exceptions\InvalidException;
use app\back\components\exceptions\UserException;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\components\helpers\Url;
use app\back\components\SessionMessages;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Input;
use app\back\modules\reports\columns\Selected;
use app\back\modules\reports\columns\Styled;
use app\back\modules\reports\columns\Tooltiped;
use Yiisoft\Db\Exception\Exception;
use Yiisoft\Db\Query\Query;

abstract class BaseReportConfig extends BaseFormConfig
{
    public const string CATEGORY_COLUMNS = 'columns';
    public const string CATEGORY_METRICS = 'metrics';
    public const string CATEGORY_GROUPS = 'groups';

    protected const bool FORBIDDEN_SPLIT_AND_GROUP_SAME_COLUMN = true;

    public bool $showSql = false;
    public bool $isHtmlVersion = false;
    public array $blocksSortOrder = []; // Custom sort order for blocks. Only values order matter
    public array $splitSortOrder = []; // Custom sort order for split columns. Only values order matter

    protected ?int $htmlVersionRowsLimit = null;

    private array $decorableColumns = [];
    private array $styledColumns = [];
    private array $tooltipedColumns = [];
    public bool $groupsMultiple = true;

    private static array $reportsClassNameMap = [];
    private static bool $someReportExecuted;
    protected ?ReportRequest $request = null;

    abstract public function defaultParams(): array;
    abstract public function columns(): array;
    abstract public function metrics(): array;
    abstract public function groups(): array;
    abstract public function filters(): array;
    abstract public function tableMap(): array;

    public function loadAndValidateOrException(array $params): self
    {
        if (isset($params['showSql'])) {
            $this->showSql = (bool)$params['showSql'];
        }

        if (isset($params['isHtmlVersion'])) {
            $this->isHtmlVersion = (bool)$params['isHtmlVersion'];
        }

        $request = new ReportRequest($params, $this->filtersNames());
        foreach ($request->invalidFilters() as $invalidFilter) {
            $this->container->get(SessionMessages::class)->info("Invalid filter removed: '$invalidFilter'");
        }

        $errors = [];

        $orRequests = $request->separatedRequestsByOrBlocks();

        if (count($orRequests) === 1) {
            throw new InvalidException('Look like incorrect use of “Or blocks” (colored border blocks). At least two or no blocks are required');
        }

        foreach ($orRequests as $orRequest) {
            $this->load($orRequest);
            if (!$this->validate()) {
                $errors[] = $this->getFirstErrors();
            }
        }

        $this->load($request);
        if (!$this->validate()) {
            $errors[] = $this->getFirstErrors();
        }

        if (!empty($errors)) {
            throw new InvalidException([
                'errors' => array_merge(...$errors),
            ]);
        }

        return $this;
    }

    protected function load(ReportRequest $request): static
    {
        $this->request = $request;
        $this->loadFiltersRules();
        return $this;
    }

    public function getRequest(): ReportRequest
    {
        return $this->request;
    }

    protected function defaultValidators(): array
    {
        return [
            ['groups', 'required', 'when' => fn() => $this->request->isChart || $this->request->block],
            ['metrics', 'required', 'when' => fn() => $this->request->isTotals],
            ['columns', 'required', 'when' => fn() => !$this->request->isTotals],
            ['columns', StringArrayValidator::class, $this->elementsNames(self::CATEGORY_COLUMNS), true],
            ['metrics', StringArrayValidator::class, $this->elementsNames(self::CATEGORY_METRICS), true],
            ['groups', StringArrayValidator::class, $this->elementsNames(self::CATEGORY_GROUPS), true],
            ['groups', fn() => $this->request->isChart && count($this->request->groups) !== 1 ? 'Only one group must be selected for chart' : null],
            ['groups', fn() => $this->request->block && $this->request->isGroupedByWithoutBlockAndSplit($this->request->block) ? 'Can\'t use same column in group when it is already in block' : null],
            ['groups', fn() => $this->request->split && $this->request->isGroupedByWithoutBlockAndSplit($this->request->split) ? 'Can\'t use same column in group when it is already in split' : null, 'when' => fn() => static::FORBIDDEN_SPLIT_AND_GROUP_SAME_COLUMN],
            ['metrics', fn() => $this->request->split && count($this->request->metrics) !== 1 ? 'Only one metric can be split to columns' : null],
            [['split', 'block'], StringInArrayValidator::class, $this->elementsNames(self::CATEGORY_GROUPS), true],
            ['split', fn() => !empty($this->request->split) && $this->request->split === $this->request->block ? 'Can\'t split by same column as block' : null],
        ];
    }

    public function configureElement(BaseColumn $element): void
    {
        if (isset($this->request)) {
            $element->isChart = $this->request->isChart;
            $element->isHtmlVersion = $this->isHtmlVersion;
        }
    }

    protected function elementsConfigs(string $category): array
    {
        return match ($category) {
            self::CATEGORY_COLUMNS => [fn() => $this->columns(), Selected::class],
            self::CATEGORY_METRICS => [fn() => $this->metrics(), Selected::class],
            self::CATEGORY_GROUPS => [fn() => $this->groups(), Selected::class],
            self::CATEGORY_FILTERS => [fn() => $this->filters(), Input::class],
            default => throw new \UnexpectedValueException(),
        };
    }

    // Default QueryConfig from ReportConfig
    public function queryConfig(): BaseQueryConfig
    {
        $name = array_slice(explode('\\', static::class), -1)[0];

        return new class ($name, $this, $this->container) extends BaseQueryConfig
        {
            public function __construct(private readonly string $name, private readonly BaseReportConfig $reportConfig, Container $container)
            {
                parent::__construct($container);
            }

            public function selects(): array
            {
                return $this->reportConfig->getRequest()->isTotals
                    ? array_merge(...array_values($this->reportConfig->groups()), ...array_values($this->reportConfig->metrics()))
                    : Arr::groupsToFlat($this->reportConfig->columns());
            }

            public function groups(): array
            {
                return Arr::groupsToFlat($this->reportConfig->groups());
            }

            public function filters(): array
            {
                return Arr::groupsToFlat($this->reportConfig->filters());
            }

            public function tableMap(): array
            {
                return $this->reportConfig->tableMap();
            }

            public function name(): string
            {
                return $this->name;
            }

            protected function initColumn(BaseColumn $column): void
            {
                $this->reportConfig->configureElement($column);
            }
        };
    }

    protected function beforeQuery(): void
    {
    }

    public function queryString(): string
    {
        $this->beforeQuery();

        return $this->queryConfig()
            ->query($this->db, $this->request->queryRequest())
            ->createCommand()
            ->getRawSql();
    }

    public function getData(): array
    {
        $this->beforeQuery();
        $query = $this->queryConfig()->query($this->db, $this->request->queryRequest());

        if ($this->htmlVersionRowsLimit) {
            $query->limit($this->htmlVersionRowsLimit + 1);
        }

        $rows = $this->queryAll($query);
        self::$someReportExecuted = true;

        if ($this->htmlVersionRowsLimit && count($rows) > $this->htmlVersionRowsLimit) {
            array_pop($rows);
            $this->container->get(SessionMessages::class)->warning("Result too big and limited to {$this->htmlVersionRowsLimit} rows!");
        }

        return $rows;
    }

    public function getIterator(): iterable
    {
        $this->beforeQuery();
        $query = $this->queryConfig()->query($this->db, $this->request->queryRequest());

        return $query->createCommand()->query();
    }

    public function getColumns(bool $assocColumns = false): array
    {
        $allColumnsWithBlocks = $this->allSelectableColumns();

        $columns = array_map(static fn ($arr) => $arr[1], Arr::leaveOnlyKeys($allColumnsWithBlocks, $this->request->queryRequest()->selects())); // TODO: fix second $this->queryRequest() call
        self::fixAmbiguousColumnsTitles($allColumnsWithBlocks, $columns);

        $this->decorableColumns = array_filter($columns, function ($c, $k) {
            if ($this->request->isChart && !in_array($k, array_filter([$this->request->split, $this->request->block]), true)) {
                return false; // Decorate only split and block columns
            }

            return $c instanceof Decorated;
        }, ARRAY_FILTER_USE_BOTH);

        if ($this->isHtmlVersion) {
            $this->styledColumns = array_filter($columns, static fn($c) => $c instanceof Styled);
            $this->tooltipedColumns = array_filter($columns, static fn($c) => $c instanceof Tooltiped);
        }

        if ($assocColumns) {
            $columns = array_map(static fn(BaseColumn $c) => $c->title, $columns);
        }

        return $columns;
    }

    public function decorate(array &$rows, array $columns): void
    {
        $this->beforeDecorate($rows, $columns);

        foreach ($rows as &$row) {
            $this->decorateRow($row);
        }
    }

    public function setHtmlVersionRowsLimit(?int $limit): void
    {
        $this->htmlVersionRowsLimit = $limit;
    }

    public function dataAndColumns(bool $assocColumns = true, bool $decorate = true): array
    {
        $data = $this->getData();
        $columns = $this->getColumns();

        if ($decorate) {
            $this->decorate($data, $columns);
        }

        if ($this->request->isTotals && $this->request->split) {
            if (count($this->request->metrics) !== 1) {
                throw new \LogicException("Can't use pivot without metric or more than 1 metric");
            }
            $metric = $this->request->metrics[0];
            /** @var BaseColumn $metricColumn */
            $metricColumn = $columns[$metric];

            unset($columns[$metric]);
            if (!$this->request->isGroupedByWithoutBlockAndSplit($this->request->split)) {
                unset($columns[$this->request->split]); // Useless, but valid case, when group == split
            }

            ['data' => $data, 'groups' => $splitGroups] = Arr::pivot($data, $this->request->split, $metric, array_keys($columns));

            $splitColumns = [];
            foreach ($splitGroups as $splitGroup) {
                $groupColumn = clone $metricColumn;
                $groupColumn->title = (string) $splitGroup;
                $splitColumns[$splitGroup] = $groupColumn;
            }
            if (isset($this->splitSortOrder)) {
                $splitColumns = Arr::sortByKeys($splitColumns, $this->splitSortOrder);
            }

            $columns = array_merge($columns, $splitColumns);
        }

        if ($assocColumns) {
            $columns = array_map(static fn(BaseColumn $c) => $c->title, $columns);
        }

        return ['data' => $data, 'columns' => $columns];
    }

    private function queryAll(Query $query): array
    {
        try {
            return $query->createCommand()->queryAll();
        } catch (Exception $e) {
            if (isset($e->errorInfo[0]) && $e->errorInfo[0] === '57014' && $e->errorInfo[2] === 'ERROR:  canceling statement due to user request') {
                throw new UserException("Query canceled");
            }

            if (isset($e->errorInfo[0]) && $e->errorInfo[0] === '40001' && $e->errorInfo[2] === 'ERROR:  canceling statement due to conflict with recovery') {
                throw new UserException("Query take too long and must be restarted");
            }

            throw $e;
        }
    }

    protected function beforeDecorate(array &$rows, array &$columns): void
    {
    }

    private static function fixAmbiguousColumnsTitles(array $allColumnsWithBlocks, array $selectedColumns): void
    {
        $titles = array_map(static fn(BaseColumn $c) => $c->title, $selectedColumns);

        $columnBlockNames = [];
        foreach ($allColumnsWithBlocks as $columnName => [$blockName, $c]) {
            /** @var BaseColumn $c */
            $title = $c->title;
            if (in_array($title, $titles, true)) {
                $columnBlockNames[$title][$columnName] = $blockName;
            }
        }

        // Example:           [ Col title       => [col name => block name, col name 2 => block name 2, col name 3 => block name 3]];
        // $ambiguousTitles = ['Traffic source' => ['pay_ts' => 'Pay',      'log_ts'   => 'Login',      'reg_ts'   => 'Reg']];
        $ambiguousTitles = array_filter($columnBlockNames, static fn($columns) => count($columns) > 1);

        foreach ($selectedColumns as $name => $col) {
            /** @var BaseColumn $col */
            $title = $col->title;
            if (array_key_exists($title, $ambiguousTitles)) {
                $col->title = "$title ({$ambiguousTitles[$title][$name]})";
            }
        }
    }

    private function allSelectableColumns(): array
    {
        $elementsCategories = $this->request->isTotals ? [self::CATEGORY_GROUPS, self::CATEGORY_METRICS] : [self::CATEGORY_COLUMNS];
        $allColumnsBlocks = [];
        foreach ($elementsCategories as $cat) {
            foreach ($this->elements($cat) as $blockName => $cols) {
                foreach ($cols as $colName => $column) {
                    $allColumnsBlocks[$colName] = [$blockName, $column];
                }
            }
        }

        return $allColumnsBlocks;
    }

    // Decorate one row (and return it, for large CSV export)
    public function decorateRow(array &$row): array
    {
        $originalRow = $row;
        foreach ($this->decorableColumns as $columnName => $column) {
            /** @var Decorated $column */
            $row[$columnName] = $column->decorate($row[$columnName], $originalRow);
        }

        foreach ($this->styledColumns as $columnName => $column) {
            /** @var Styled $column */
            $styles = $column->styles($originalRow[$columnName], $originalRow);

            if (!empty($styles)) {
                $row['__style__' . $columnName] = $styles;
            }
        }

        foreach ($this->tooltipedColumns as $columnName => $column) {
            /** @var Tooltiped $column */
            $tooltip = $column->tooltip($originalRow[$columnName], $originalRow);

            if (!empty($tooltip)) {
                $row['__title__' . $columnName] = $tooltip;
            }
        }

        return $row;
    }

    public static function snakeName(): string
    {
        if (!isset(self::$reportsClassNameMap[static::class])) {
            self::$reportsClassNameMap[static::class] = Str::camel2id(array_slice(explode('\\', substr(static::class, 0, -6)), -1)[0]);
        }

        return self::$reportsClassNameMap[static::class];
    }

    public static function url(array $params, ?string $reportConfigClass = null): string
    {
        if ($reportConfigClass === null) {
            $reportConfigClass = (string)static::class;
        }

        if ($reportConfigClass === self::class) {
            throw new \LogicException("Can't use url in parent class");
        }
        foreach ($params['filters'] ?? [] as $i => $filter) {
            if (is_array($filter[1])) {
                $params['filters'][$i][1] = ',' . implode(',', $filter[1]);
            }
        }

        $filters = BaseFormRequest::formFilters($params['filters'] ?? []);
        $isTotals = $params['isTotals'] ?? false;
        unset($params['filters'], $params['isTotals']);

        foreach ($params as &$values) {
            if (is_array($values)) {
                $values = ',' . implode(',', $values);
            }
        }

        return Url::to('/reports/' . static::snakeName(), array_merge($params, $filters, ['#' => $isTotals ? 'totals' : 'submit']));
    }

    public static function isSomeReportExecuted(\Closure $closure): bool
    {
        self::$someReportExecuted = false;
        ($closure)();
        $someReportExecuted = self::$someReportExecuted;
        self::$someReportExecuted = false;
        return $someReportExecuted;
    }
}
