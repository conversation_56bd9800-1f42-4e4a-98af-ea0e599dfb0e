<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusLog;

use app\back\components\helpers\Date;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\EmployeeEmailColumn;
use app\back\modules\reports\columns\JsonFieldColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\BonusLogs;
use app\back\repositories\Users;

class BonusLogConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            [['created_at'], 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['created_at', Date::monthBegin(), Operators::GE],
            ['created_at', Date::yesterday(), Operators::LE],
            ['site_user', ''],
            ['columns', ['site_id', 'user_id', 'site_user', 'type_id', 'bonus', 'created_at', 'operator_id', 'extra', 'sum', 'currency', 'admin_comment']],
            ['metrics', ['sum']],
        ];
    }

    public function columns(): array
    {
        return [
            'Main' => [
                ...$this->commonElements(),
                'created_at' => [DateColumn::class, ['bl' => 'created_at']],
                'extra' => [BonusLogExtraColumn::class, 'bl'],
                'sum' => [MoneyColumn::class, ['bl' => 'sum'], 'title' => 'Sum'],
                'currency' => [CurrencyColumn::class, 'bl'],
                'admin_comment' => [SimpleColumn::class, ['bl' => 'admin_comment'], 'title' => 'Admin comment'],
            ]
        ];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'sum' => [MoneyColumn::class, ['expr' => 'SUM(bl.sum)', 'bl'], 'title' => 'Sum']
            ]
        ];
    }

    public function groups(): array
    {
        return [
            'Main' => [
                ...$this->commonElements(),
                'day' => [DayColumn::class, ['bl' => 'created_at']],
            ]
        ];
    }

    public function filters(): array
    {
        return [
            'Main' => [
                ...$this->commonElements(),
                'created_at' => [DateColumn::class, ['bl' => 'created_at']],
                'operator_type' => [BonusLogOperatorTypeColumn::class, 'bl'],
                'admin_comment' => [JsonFieldColumn::class, ['bl' => 'extra'], 'key' => 'adminComment', 'title' => 'Admin comment'],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'bl' => [BonusLogs::TABLE_NAME],
            'u' => [Users::TABLE_NAME, 'u.site_id = bl.site_id AND u.user_id = bl.user_id'],
        ];
    }

    private function commonElements(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'bl'],
            'user_id' => [UserIdColumn::class, 'bl'],
            'site_user' => [SiteUserColumn::class, 'bl'],
            'brand_id' => [BrandColumn::class, 'u'],
            'type_id' => [BonusLogTypeIdColumn::class, 'bl'],
            'bonus' => [BonusLogBonusColumn::class, 'bl'],
            'operator_id' => [EmployeeEmailColumn::class, ['bl' => 'operator_id']],
        ];
    }
}
