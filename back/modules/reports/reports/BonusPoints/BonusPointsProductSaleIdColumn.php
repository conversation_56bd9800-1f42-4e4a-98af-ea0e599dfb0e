<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusPoints;

use app\back\modules\reports\columns\BaseIdColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use Yiisoft\Db\Connection\ConnectionInterface;

class BonusPointsProductSaleIdColumn extends BaseIdColumn
{
    public string $title = 'Sale ID';
    public string $column = 'id';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
