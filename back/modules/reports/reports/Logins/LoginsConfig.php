<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Logins;

use app\back\components\helpers\Date;
use app\back\entities\AffParam;
use app\back\entities\UserIgnoreId;
use app\back\modules\reports\columns\AffDataColumn;
use app\back\modules\reports\columns\AffParamsColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CountUniqUsersColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\HostColumn;
use app\back\modules\reports\columns\Hour4Column;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\IpColumn;
use app\back\modules\reports\columns\IpSubnetColumn;
use app\back\modules\reports\columns\IsOfficeIpColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\Minute10Column;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\RefcodeAppIdColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\RefcodeCrmChannelColumn;
use app\back\modules\reports\columns\RefcodePublisherColumn;
use app\back\modules\reports\columns\SiteHostForToxicUserColumn;
use app\back\modules\reports\columns\SiteHostIsAppColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\SocialNetColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UseragentAppColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentBrowserColumn;
use app\back\modules\reports\columns\UseragentBrowserVersionColumn;
use app\back\modules\reports\columns\UseragentColumn;
use app\back\modules\reports\columns\UseragentDeviceColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformVersionColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UseragentVariantVersionColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserLocaleColumn;
use app\back\modules\reports\columns\UserLoginMethodColumn;
use app\back\modules\reports\columns\UserLoginSuccessColumn;
use app\back\modules\reports\columns\UserLoyaltyStatusColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\columns\WeekDayColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\AffData;
use app\back\repositories\AffParams;
use app\back\repositories\Cities;
use app\back\repositories\Hosts;
use app\back\repositories\LoyaltyStatuses;
use app\back\repositories\MarketingTids;
use app\back\repositories\Refcodes;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\UserLoyalties;
use app\back\repositories\Users;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

class LoginsConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            ['date', 'required'],
            ['site_id', 'required', 'when' => fn() => $this->request->allEmptyFilter('site_user', 'cid')],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', Date::monthBegin(), '>='],
            ['date', Date::yesterday(), '<='],
            ['site_id', []],
            ['success', [1]],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['columns', ['site_id', 'user_id', 'date', 'ip', 'country', 'platform_id', 'browser_id']],
            ['metrics', ['count', 'count_uniq']],
        ];
    }

    public function filters(): array
    {
        return [
            'Login' => [
                'date' => [DateColumn::class, 'ul'],
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'site_user' => [SiteUserColumn::class, 'ul'],
                'success' => [UserLoginSuccessColumn::class, 'ul'],
                'fail_reason' => [UserLoginFailReasonColumn::class, 'ul'],
                'method' => [UserLoginMethodColumn::class, 'ul'],
                'social_net' => [SocialNetColumn::class, 'ul'],
                'reg' => [UserLoginRegColumn::class, 'ul'],
                'impersonal' => [UserLoginImpersonalColumn::class, 'ul'],
                'ignore' => [IgnoreColumn::class, 'ul'],
                'country' => [CountryColumn::class, 'ul'],
                'city' => [CityColumn::class, 'c'],
                ...$this->ipColumns(),
                'is_office_ip' => [IsOfficeIpColumn::class, 'ul'],
                'host' => [HostColumn::class, 'ul'],
                'host_is_app' => [SiteHostIsAppColumn::class, 'sh'],
            ],
            'Traffic' => $this->trafficSourceColumns(),
            'Useragent' => [
                'useragent' => [UseragentColumn::class, 'uag'],
                'platform_id' => [UseragentPlatformColumn::class, 'uag'],
                'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
                'browser_id' => [UseragentBrowserColumn::class, 'uag'],
                'variant_id' => [UseragentVariantColumn::class, 'uag'],
                'app_id' => [UseragentAppColumn::class, 'uag'],
                'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
            ],
            'User (reg)' => [
                'reg_date' => [DateColumn::class, 'u'],
                'cid' => [UserCidColumn::class, 'u'],
                'user_status' => [UserStatusColumn::class, 'u'],
                'reg_refcode' => [RefcodeColumn::class, 'r_reg'],
                'reg_ts_id' => [TrafficSourceColumn::class, 'r_reg'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'reg_social_net' => [SocialNetColumn::class, 'u'],
                'reg_webmaster_id' => [WebmasterIdColumn::class, 'r_reg'],
                'reg_publisher' => [MarketingTidPublisherColumn::class, 'tids_reg'],
                'reg_publisher_from_refcode' => [RefcodePublisherColumn::class, 'r_reg'],
                'reg_aff_owner' => [WebmasterAffOwnerColumn::class, 'w_reg', 'refcodeTableAlias' => 'r_reg'],
                'reg_brand_id' => [BrandColumn::class, 'u'],
                'location' => [LocationColumn::class, 'u'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'Login' => [
                'date' => [DateColumn::class, 'ul'],
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'site_user' => [SiteUserColumn::class, 'ul'],
                'success' => [UserLoginSuccessColumn::class, 'ul'],
                'fail_reason' => [UserLoginFailReasonColumn::class, 'ul'],
                'method' => [UserLoginMethodColumn::class, 'ul'],
                'social_net' => [SocialNetColumn::class, 'ul'],
                'reg' => [UserLoginRegColumn::class, 'ul'],
                'impersonal' => [UserLoginImpersonalColumn::class, 'ul'],
                'country' => [CountryColumn::class, 'ul'],
                'city' => [CityColumn::class, 'c'],
                ...$this->ipColumns(),
                'is_office_ip' => [IsOfficeIpColumn::class, 'ul'],
                'host' => [HostColumn::class, 'ul'],
                'host_toxic' => [SiteHostForToxicUserColumn::class, 'sh'],
            ],
            'Traffic' => $this->trafficSourceColumns(),
            'Useragent' => $this->useragentColumns(),
        ];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'count' => [CountColumn::class, 'ul'],
                'count_uniq' => [CountUniqUsersColumn::class, 'ul'],
            ],
        ];
    }

    public function groups(): array
    {
        return [
            'Login' => [
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'site_user_id' => [SiteUserColumn::class, 'ul'],
                'month' => [MonthColumn::class, ['ul' => 'date']],
                'week' => [WeekColumn::class, ['ul' => 'date']],
                'weekday' => [WeekDayColumn::class, ['ul' => 'date']],
                'day' => [DayColumn::class, ['ul' => 'date']],
                'hour_4' => [Hour4Column::class, ['ul' => 'date']],
                'hour' => [HourColumn::class, ['ul' => 'date']],
                'minute_10' => [Minute10Column::class, ['ul' => 'date']],
                'success' => [UserLoginSuccessColumn::class, 'ul'],
                'fail_reason' => [UserLoginFailReasonColumn::class, 'ul'],
                'method' => [UserLoginMethodColumn::class, 'ul'],
                'social_net' => [SocialNetColumn::class, 'ul'],
                'reg' => [UserLoginRegColumn::class, 'ul'],
                'impersonal' => [UserLoginImpersonalColumn::class, 'ul'],
                'country' => [CountryColumn::class, 'ul'],
                'city' => [CityColumn::class, 'c'],
                ...$this->ipColumns(),
                'host' => [HostColumn::class, 'ul'],
                'host_is_app' => [SiteHostIsAppColumn::class, 'sh'],
                'host_toxic' => [SiteHostForToxicUserColumn::class, 'sh'],
            ],
            'Traffic' => $this->trafficSourceColumns(),
            'Useragent' => $this->useragentColumns(),
            'User (reg)' => [
                'reg_date' => [DateColumn::class, 'u'],
                'user_status' => [UserStatusColumn::class, 'u'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'cid' => [UserCidColumn::class, 'u'],
                'reg_social_net' => [SocialNetColumn::class, 'u'],
                'reg_webmaster_id' => [WebmasterIdColumn::class, 'r_reg'],
                'reg_publisher' => [MarketingTidPublisherColumn::class, 'tids_reg'],
                'reg_publisher_from_refcode' => [RefcodePublisherColumn::class, 'r_reg'],
                'reg_aff_owner' => [WebmasterAffOwnerColumn::class, 'w_reg', 'refcodeTableAlias' => 'r_reg'],
                'reg_country' => [CountryColumn::class, 'u'],
                'locale' => [UserLocaleColumn::class, 'u'],
                'reg_brand_id' => [BrandColumn::class, 'u'],
                'location' => [LocationColumn::class, 'u'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'ul' => [UserLogins::TABLE_NAME],
            'r' => [Refcodes::TABLE_NAME, 'r.id = ul.refcode_id'],
            'u' => [Users::TABLE_NAME, 'u.site_id = ul.site_id AND u.user_id = ul.user_id'],
            'uag' => [Useragents::TABLE_NAME, 'uag.id = ul.useragent_id'],
            'uaga' => [UseragentApps::TABLE_NAME, 'uaga.id = uag.app_id', ['uag']],
            'uagp' => [UseragentPlatforms::TABLE_NAME, 'uagp.id = uag.platform_id', ['uag']],
            'tid' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r', 'tid'), ['r']],
            'r_reg' => [Refcodes::TABLE_NAME, 'r_reg.id = u.refcode_id', ['u']],
            'ad' => [AffData::TABLE_NAME, 'ad.id = ul.aff_data_id'],
            'ap' => [AffParams::paramsAsObjJoinQuery('ul'), '', [], 'CROSS JOIN LATERAL'],
            'sh' => [Hosts::TABLE_NAME, 'sh.id = ul.host_id'],
            'w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('w', 'r'), ['r']],
            'uly' => [UserLoyalties::TABLE_NAME, 'uly.site_id = ul.site_id AND uly.user_id = ul.user_id'],
            'ls' => [LoyaltyStatuses::TABLE_NAME, 'ls.site_id = uly.site_id AND ls.id = COALESCE(uly.status_id, 1)', ['uly']],
            'w_reg' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('w_reg', 'r_reg'), ['r_reg']],
            'tids_reg' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_reg', 'tids_reg'), ['r_reg']],
            'c' => [Cities::TABLE_NAME, 'c.id = ul.city_id'],
        ];
    }

    private function trafficSourceColumns(): array
    {
        return [
            'refcode' => [RefcodeColumn::class, 'r'],
            'ts_id' => [TrafficSourceColumn::class, 'r'],
            'webmaster_id' => [WebmasterIdColumn::class, 'r'],
            'aff_owner' => [WebmasterAffOwnerColumn::class, 'w', 'refcodeTableAlias' => 'r'],
            'ref_app_id' => [RefcodeAppIdColumn::class, 'r'],
            'aff_data' => [AffDataColumn::class, 'ad'],
            'aff_params' => [AffParamsColumn::class, 'ap'],
            'publisher' => [MarketingTidPublisherColumn::class, 'tid'],
            'crm_channel' => [RefcodeCrmChannelColumn::class, 'r'],
        ];
    }

    private function useragentColumns(): array
    {
        return [
            'useragent' => [UseragentColumn::class, 'uag'],
            'platform_id' => [UseragentPlatformColumn::class, 'uag'],
            'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
            'platform_version' => [UseragentPlatformVersionColumn::class, 'uag'],
            'browser_id' => [UseragentBrowserColumn::class, 'uag'],
            'browser_version' => [UseragentBrowserVersionColumn::class, 'uag'],
            'device_id' => [UseragentDeviceColumn::class, 'uag'],
            'variant_id' => [UseragentVariantColumn::class, 'uag'],
            'variant_version' => [UseragentVariantVersionColumn::class, 'uag'],
            'app_id' => [UseragentAppColumn::class, 'uag'],
            'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
        ];
    }

    private function ipColumns(): array
    {
        return [
            'ip' => [IpColumn::class, 'ul'],
            'ip_subnet_8' => [IpSubnetColumn::class, 'ul', 'title' => 'Ip subnet (8)', 'subnet' => 8],
            'ip_subnet_16' => [IpSubnetColumn::class, 'ul', 'title' => 'Ip subnet (16)', 'subnet' => 16],
            'ip_subnet_24' => [IpSubnetColumn::class, 'ul', 'title' => 'Ip subnet (24)', 'subnet' => 24],
        ];
    }
}
