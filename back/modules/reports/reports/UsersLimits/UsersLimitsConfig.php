<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersLimits;

use app\back\components\helpers\Date;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\UserLimits;

class UsersLimitsConfig extends BaseReportConfig
{
    public function rules(): array
    {
        return [
            [['created_at', 'site_id'], 'required']
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['created_at', Date::monthBegin(), '>='],
            ['created_at', Date::yesterday(), '<='],
            ['site_id', []],
            ['columns', ['site_id', 'user_id', 'limit_type']],
            ['metrics', ['count']],
        ];
    }

    public function filters(): array
    {
        return [
            'Main' => [
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'created_at' => [DateColumn::class, ['ul' => 'created_at'], 'title' => 'Created at'],
                'action_type' => [UsersLimitsActionTypeColumn::class, 'ul'],
                'limit_type' => [UsersLimitsTypeColumn::class, 'ul'],
                'interval' => [UsersLimitsIntervalColumn::class, 'ul'],
                'period' => [UsersLimitsPeriodColumn::class, 'ul'],
                'initiator' => [UsersLimitsInitiatorColumn::class, 'ul'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'General' => [
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'limit_id' => [SimpleColumn::class, ['ul' => 'limit_id'], 'title' => 'Limit id'],
                'status' => [UsersLimitsStatusColumn::class, 'ul'],
                'action_type' => [UsersLimitsActionTypeColumn::class, 'ul'],
                'limit_type' => [UsersLimitsTypeColumn::class, 'ul'],
                'start_at' => [DateColumn::class, ['ul' => 'start_at'], 'title' => 'Start at'],
                'finish_at' => [DateColumn::class, ['ul' => 'finish_at'], 'title' => 'Finish at'],
                'canceled_at' => [DateColumn::class, ['ul' => 'canceled_at'], 'title' => 'Canceled at'],
                'period' => [UsersLimitsPeriodColumn::class, 'ul'],
                'amount' => [MoneyColumn::class, ['ul' => 'amount'], 'title' => 'Amount'],
                'currency' => [CurrencyColumn::class, 'ul'],
                'interval' => [UsersLimitsIntervalColumn::class, 'ul'],
                'reason' => [SimpleColumn::class, ['ul' => 'reason'], 'title' => 'Reason'],
                'initiator' => [UsersLimitsInitiatorColumn::class, 'ul'],
                'created_at' => [DateColumn::class, ['ul' => 'created_at'], 'title' => 'Created at'],
            ],
        ];
    }

    public function metrics(): array
    {
        return [
            'Main' => [
                'count' => [CountColumn::class, 'ul'],
                'sum' => [MoneyColumn::class, ['expr' => 'SUM(ul.amount)', 'ul'], 'title' => 'Sum'],
            ],
        ];
    }

    public function groups(): array
    {
        return [
            'Main' => [
                'site_id' => [SiteIdColumn::class, 'ul'],
                'user_id' => [UserIdColumn::class, 'ul'],
                'action_type' => [UsersLimitsActionTypeColumn::class, 'ul'],
                'limit_type' => [UsersLimitsTypeColumn::class, 'ul'],
                'period' => [UsersLimitsPeriodColumn::class, 'ul'],
                'initiator' => [UsersLimitsInitiatorColumn::class, 'ul'],
                'currency' => [CurrencyColumn::class, 'ul'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'ul' => [UserLimits::TABLE_NAME],
        ];
    }
}
