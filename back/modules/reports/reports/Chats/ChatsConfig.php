<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Chats;

use app\back\entities\YhEvent;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountFilterColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Chats;
use app\back\repositories\Users;
use app\back\repositories\YhEvents;

class ChatsConfig extends BaseReportConfig
{
    private string $totalMessages = '(c.client_message_count + c.operator_message_count)';

    public function defaultParams(): array
    {
        return [
            ['site_id', []],
            ['columns', ['chat_id', 'user_id', 'created_at', 'total_messages', 'chat_duration', 'satisfaction_level', 'site_id', 'topic_id']],
        ];
    }

    public function filters(): array
    {
        return [
            '' => [
                ...$this->commonColumns(),
                'created_at' => [DateColumn::class, ['c' => 'created_at'], 'title' => 'Created'],
                'messages_count' => [CountFilterColumn::class, 'c', 'expression' => $this->totalMessages, 'title' => 'Messages count'],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            '' => [
                ...$this->commonColumns(),
                'created_at' => [DateColumn::class, ['c' => 'created_at'], 'title' => 'Created'],
                'total_messages' => [CountColumn::class, ['expr' => $this->totalMessages, 'c'], 'title' => 'Messages count'],
                'chat_duration' => [ChatDurationColumn::class, 'c'],
                'satisfaction_level' => [YhEventSatisfactionLevelColumn::class, 'e'],
            ],
        ];
    }

    public function metrics(): array
    {
        return [
            '' => [
                'count_users' => [CountColumn::class, ['expr' => 'COUNT(DISTINCT (c.site_id, c.user_id))', 'c'], 'title' => 'Users count'],
                'count_chats' => [CountColumn::class, ['expr' => 'COUNT(*)', 'c'], 'title' => 'Chats count'],
                'count_messages' => [CountColumn::class, ['expr' => "SUM($this->totalMessages)", 'c'], 'title' => 'Messages count'],
            ]
        ];
    }

    public function groups(): array
    {
        return [
            '' => [
                ...$this->commonColumns(),
                'day_created' => [DayColumn::class, ['c' => 'created_at'], 'title' => 'Day (created)'],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'c' => [Chats::TABLE_NAME],
            'e' => [YhEvents::TABLE_NAME, 'e.source_id = c.chat_id AND e.status_id = ' . YhEvent::STATUS_CLIENT_RATE],
            'u' => [Users::TABLE_NAME, 'u.site_id = c.site_id AND u.user_id = c.user_id'],
        ];
    }

    private function commonColumns(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'c'],
            'brand_id' => [BrandColumn::class, 'u'],
            'user_id' => [UserIdColumn::class, 'c'],
            'topic_id' => [ChatTopicColumn::class, 'c'],
            'reg_country' => [CountryColumn::class, 'u'],
        ];
    }
}
