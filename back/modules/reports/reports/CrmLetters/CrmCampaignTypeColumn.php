<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CrmLetters;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\CrmCampaign;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class CrmCampaignTypeColumn extends BaseColumn implements Filtered, Selected
{
    use FilterAndSelectDefault;

    public string $column = 'type';
    public string $title = 'Campaign type';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(CrmCampaign::TYPES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, CrmCampaign::TYPES];
    }
}
