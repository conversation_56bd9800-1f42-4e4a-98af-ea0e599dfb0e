<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BettingSummary;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Date;
use app\back\entities\enums\WpAffOwner;
use app\back\entities\Site;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\PercentColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

class BettingSummaryConfig extends BaseReportConfig
{
    private array $groupPeriods = [];

    public function rules(): array
    {
        return [
            [['site_id', 'date'], 'required'],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', Date::monthBegin(), '>='],
            ['date', Date::yesterday(), '<='],
            ['site_id', [Site::GGB]],
            ['metrics', array_keys(Arr::flatten($this->metrics()))],
            ['groups', ['day']],
        ];
    }

    protected function beforeQuery(): void
    {
        foreach (['day', 'week', 'month'] as $period) {
            if ($this->request->isGroupedBy($period)) {
                $this->groupPeriods[] = $period;
                $this->request->orders[$period] = SORT_DESC;
            }
        }

        parent::beforeQuery();
    }

    public function filters(): array
    {
        return [
            'Main' => [
                'date' => [DateColumn::class, ['d:nl', 'v:nl', 'ul:nl', 'u:nl', 'us:nl', 'bb:nl']],
                'country' => [CountryColumn::class, ['c:nl', 'v:nl', 'ul:nl', 'u:nl', 'us:nl', 'bb:nl']],
                'site_id' => [SiteIdColumn::class, ['v:nl', 'ul:nl', 'u:nl', 'us:nl', 'bb:nl']],
            ],
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        $paymentMetrics = [];
        foreach (['in', 'out'] as $dir) {
            $paymentMetrics["{$dir}_users"] = [CountColumn::class, ['expr' => "SUM({$dir}_users)", 'us:n'], 'title' => "Users ({$dir})"];
            $paymentMetrics["{$dir}_sum"] = [MoneyColumn::class, ['expr' => "SUM({$dir}_sum)", 'us:n'], 'title' => "Sum ({$dir})"];
            $paymentMetrics["{$dir}_count"] = [CountColumn::class, ['expr' => "SUM({$dir}_count)", 'us:n'], 'title' => "Count ({$dir})"];
        }

        $bettingMetrics = [
            'bets_total' => [CountColumn::class, ['expr' => 'SUM(bb.bets_total)', 'bb:n'], 'title' => 'Bets total'],
        ];
        foreach (array_keys(BsBettingConfig::BET_STATUSES) as $status) {
            $bettingMetrics["bets_{$status}"] =  [CountColumn::class, ['expr' => "SUM(bets_{$status})", 'bb:n'], 'title' => "Bets ($status)"];
        }

        return [
            'Visits' => [
                'visits' => [CountColumn::class, ['expr' => 'SUM(v.visits)', 'v:n'], 'title' => 'Visits'],
            ],
            'Logins' => [
                'logins_success' => [CountColumn::class, ['expr' => 'SUM(logins_success)', 'ul:n'], 'title' => 'Logins success'],
                'logins_fail' => [CountColumn::class, ['expr' => 'SUM(logins_fail)', 'ul:n'], 'title' => 'Logins fail'],
                'logins_per_visit' => [PercentColumn::class, ['expr' => '(SUM(ul.logins_success)::real / NULLIF(SUM(v.visits), 0) * 100)::decimal(10, 1)', 'v:n' => 'visits', 'ul:n' => 'logins_success'], 'title' => 'Logins per visit'],
            ],
            'Regs' => [
                'regs' => [CountColumn::class, ['expr' => 'SUM(regs)', 'u:n'], 'title' => 'Regs'],
                'regs_per_visit' => [PercentColumn::class, ['expr' => '(SUM(u.regs)::real / NULLIF(SUM(v.visits), 0) * 100)::decimal(10, 1)', 'v:n' => 'visits', 'u:n' => 'regs'], 'title' => 'Regs per visit'],
            ],
            'FD' => [
                'fd_count' => [CountColumn::class, ['expr' => "SUM(us.fd_count)", 'us:n'], 'title' => 'Fd count'],
                'fd_per_reg' => [PercentColumn::class, ['expr' => '(SUM(us.fd_count)::real / NULLIF(SUM(u.regs), 0) * 100)::decimal(10, 1)', 'us', 'us:n' => 'fd_count', 'u:n' => 'regs'], 'title' => 'Fd per reg'],
            ],
            'Bets (new)' => [
                'bets_new' => [CountColumn::class, ['expr' => "SUM(bb.bets_new)", 'bb:n'], 'title' => 'Bets new'],
                'bets_new_per_reg' => [PercentColumn::class, ['expr' => '(SUM(bb.bets_new)::real / NULLIF(SUM(u.regs), 0) * 100)::decimal(10, 1)', 'bb:n' => 'bets_new', 'u:n' => 'regs'], 'title' => 'Bets new per reg'],
            ],
            'Combined' => [
                'dau' => [CountColumn::class, ['expr' => "SUM(bb.dau)", 'bb:n'], 'title' => 'DAU'],
                'turnover' => [MoneyColumn::class, ['expr' => 'SUM(bb.turnover)', 'bb:n'], 'title' => 'Turnover'],

                'in_out' => [MoneyColumn::class, ['expr' => 'SUM(us.in_sum - us.out_sum)', 'us:n' => ['in_sum', 'out_sum']], 'title' => 'In-out'],
                'ggr' => [MoneyColumn::class, ['expr' => 'SUM(bb.ggr)', 'bb:n'], 'title' => 'GGR'],
            ],
            'Payments' => $paymentMetrics,
            'Betting' => $bettingMetrics,
        ];
    }

    public function groups(): array
    {
        $deps = ['d:nl', 'v:nl', 'ul:nl', 'u:nl', 'us:nl', 'bb:nl'];

        return [
            'Dates' => [
                'month' => [MonthColumn::class, ['expr' => 'd.month', ...$deps]],
                'week' => [WeekColumn::class, ['expr' => 'd.week', ...$deps]],
                'day' => [DayColumn::class, ['expr' => 'd.day', ...$deps]],
            ],
            'Other' => [
                'country' => [CountryColumn::class, ['expr' => 'c.country', 'c:n', 'v:nl', 'ul:nl', 'u:nl', 'us:nl', 'bb:nl']],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'd' => [function (Query $query, QueryRequest $nestedRequest) {
                $excludeWebmastersQuery = (new Query($this->db))
                    ->select('w.id')
                    ->from(['w' => WpWebmasters::TABLE_NAME])
                    ->where([WpAffOwner::groupNameByNameExpression('w.aff_owner') => [WpAffOwner::GROUP_VB, WpAffOwner::GROUP_VP]]);

                $query->withQuery($excludeWebmastersQuery, 'w');

                if (empty($this->groupPeriods)) {
                    return "(VALUES(1))"; // Fake table with one row for join without condition
                }

                return (new BsDatesConfig($this->container))->query($this->db, $nestedRequest);
            }],
            'c' => [function (Query $query, QueryRequest $nestedRequest) {
                if (!$this->request->isGroupedBy('country')) {
                    return "(VALUES(1))"; // Fake table with one row for join without condition
                }

                return (new BsCountriesConfig($this->container))->query($this->db, $nestedRequest);
            }, 'true'],
            'v' => [BsVisitsConfig::class, $this->metricJoinConditions('v'), ['d']],
            'ul' => [BsLoginsConfig::class, $this->metricJoinConditions('ul'), ['d']],
            'u' => [BsUsersConfig::class, $this->metricJoinConditions('u'), ['d']],
            'us' => [BsPaymentsConfig::class, $this->metricJoinConditions('us'), ['d']],
            'bb' => [BsBettingConfig::class, $this->metricJoinConditions('bb'), ['d']],
        ];
    }

    private function metricJoinConditions(string $alias): string
    {
        $joinConditions = [];

        foreach ($this->groupPeriods as $period) {
            $joinConditions[] = "$alias.$period = d.$period";
        }

        if ($this->request->isGroupedBy('country')) {
            $joinConditions[] = "$alias.country = c.country";
        }

        return implode(' AND ', $joinConditions) ?: 'true';
    }
}
