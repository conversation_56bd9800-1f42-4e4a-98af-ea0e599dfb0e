<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Kpi;

use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\repositories\views\UserGameTokenAggs;

class KpiGamesQueryConfig extends KpiBaseMetricQueryConfig
{
    protected bool $excludeIgnoreUsers = false;

    protected function mainTableAlias(): string
    {
        return 'ugta';
    }

    protected function dateConfigArray(): array
    {
        return ['ugta' => 'day'];
    }

    public function filters(): array
    {
        return array_merge(parent::filters(), [
            'country' => [CountryColumn::class, 'ugta'],
        ]);
    }

    public function metrics(): array
    {
        $curPostfix = mb_strtolower($this->request->getFilter('currency'));

        $dayCondition = $this->diffDays ? 'ugta.day = d.day' : 'true';
        $dauExpression = $this->diffDays ? "SUM(hll_cardinality(ugta.users)) FILTER (WHERE $dayCondition)   " : "hll_cardinality(hll_union_agg(ugta.users) FILTER (WHERE $dayCondition))";

        return [
            'dau' => [CountColumn::class, ['expr' => $dauExpression, 'ugta']],
            'ggr' => [MoneyColumn::class, ['expr' => "COALESCE(SUM(ggr_$curPostfix) FILTER (WHERE $dayCondition), 0)", 'ugta']],
            'ggr_betting' => [MoneyColumn::class, ['expr' => "COALESCE(SUM(ggr_betting_$curPostfix) FILTER (WHERE $dayCondition), 0)", 'ugta']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'ugta' => [UserGameTokenAggs::TABLE_NAME],
        ];
    }
}
