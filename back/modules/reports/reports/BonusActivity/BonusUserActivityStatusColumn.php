<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusActivity;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\BonusUserActivity;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class BonusUserActivityStatusColumn extends BaseColumn implements Selected, Filtered, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'status';
    public string $title = 'Status';

    public function inputProps(): array
    {
        return [

            'type' => 'select',
            'list' => Arr::assocToIdName(BonusUserActivity::STATUSES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, BonusUserActivity::STATUSES];
    }

    public function decorate($value, array $row)
    {
        return BonusUserActivity::getStatus($value);
    }
}
