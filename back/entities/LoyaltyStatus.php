<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class LoyaltyStatus extends BaseEntity
{
    #[IdValidator]
    public int $site_id;
    #[IdValidator]
    public int $id;
    #[StringValidator(1, 255)]
    public string $title;
    #[IntValidator]
    public ?int $prize_points;
    #[MoneyValidator]
    public ?string $prize_amount;
    #[MoneyValidator]
    public ?string $points_to_finish;
    #[MoneyValidator]
    public ?string $total_points;
    #[IntValidator]
    public int $rank;
    #[IdValidator]
    public ?int $brand_id;
}
