<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Url;
use app\back\components\validators\{
    BigIdValidator,
    BooleanValidator,
    DateTimeImmutableValidator,
    DateValidator,
    EmailValidator,
    FilterValidator,
    IdValidator,
    IntArrayValidator,
    IntInArrayValidator,
    IntValidator,
    IpValidator,
    StringLimitedValidator,
    StringValidator
};

class User extends BaseEntity
{
    public const int BONUS_BL_STATUS_NO = 1;
    public const int BONUS_BL_STATUS_YES_AUTO = 2;
    public const int BONUS_BL_STATUS_YES_MANUAL = 3;

    public const array BONUS_BL_STATUSES = [
        User::BONUS_BL_STATUS_NO => 'No',
        User::BONUS_BL_STATUS_YES_AUTO => 'Yes (auto)',
        User::BONUS_BL_STATUS_YES_MANUAL => 'Yes (manual)',
    ];

    public const string LOCALE_RU = 'ru';

    public const int STATUS_FREE = 1;
    public const int STATUS_PAID = 2;
    public const int STATUS_NORMAL = 3;
    public const int STATUS_NEW_VIP = 4;
    public const int STATUS_PRE_VIP = 5;
    public const int STATUS_VIP = 6;
    /** @deprecated  */
    public const int STATUS_PRE_ULTRA = 7;
    public const int STATUS_ULTRA = 8;
    /** @deprecated  */
    public const int STATUS_PREFECT = 9;
    public const int STATUS_PLAYED = 10;
    public const int STATUS_PRE_NORMAL = 11;
    public const int STATUS_ASP = 12;

    public const array STATUSES = [
        self::STATUS_FREE => 'Free',
        self::STATUS_PAID => 'Paid',
        self::STATUS_PLAYED => 'Played',
        self::STATUS_PRE_NORMAL => 'Pre Normal',
        self::STATUS_NORMAL => 'Normal',
        self::STATUS_NEW_VIP => 'New VIP',
        self::STATUS_PRE_VIP => 'Pre VIP',
        self::STATUS_VIP => 'VIP',
        self::STATUS_ASP => 'ASP',
        self::STATUS_ULTRA => 'Ultra',
    ];

    public const array VIP_STATUSES = [
        self::STATUS_VIP,
        self::STATUS_ASP,
        self::STATUS_ULTRA,
    ];

    public const int NEW_VIP_DEP_SUM_RUB = 24000;
    public const int NEW_VIP_EXP_PERIOD_DAYS = 60;

    public const int ACTIVE_STATUS_ACTIVE = 1;
    /** @deprecated  */
    public const int ACTIVE_STATUS_EX = 2;
    public const int ACTIVE_STATUS_AWOL = 3;
    public const int ACTIVE_STATUS_AWOL2 = 4;
    public const int ACTIVE_STATUS_LOST_AWOL = 5;
    /** @deprecated  */
    public const int ACTIVE_STATUS_WEAK = 6;
    public const int ACTIVE_STATUS_NEW = 7;
    public const int ACTIVE_STATUS_RISK = 8;
    public const int ACTIVE_STATUS_ACT_IN = 9;
    public const int ACTIVE_STATUS_NEW_IN = 10;
    public const int ACTIVE_STATUS_JELLY = 11;
    public const int ACTIVE_STATUS_INTER = 12;
    public const int ACTIVE_STATUS_LOW = 13;
    public const int ACTIVE_STATUS_HIGH = 14;

    public const array ACTIVE_STATUSES = [
        self::ACTIVE_STATUS_ACTIVE => 'Active',
        self::ACTIVE_STATUS_EX => 'Ex',
        self::ACTIVE_STATUS_AWOL => 'Awol',
        self::ACTIVE_STATUS_AWOL2 => 'Awol 2',
        self::ACTIVE_STATUS_LOST_AWOL => 'Lost Awol',
        self::ACTIVE_STATUS_WEAK => 'Weak',
        self::ACTIVE_STATUS_NEW => 'New',
        self::ACTIVE_STATUS_RISK => 'Risk',
        self::ACTIVE_STATUS_ACT_IN => 'Act In',
        self::ACTIVE_STATUS_NEW_IN => 'New In',
        self::ACTIVE_STATUS_JELLY => 'Jelly',
        self::ACTIVE_STATUS_INTER => 'Inter',
        self::ACTIVE_STATUS_LOW => 'Low',
        self::ACTIVE_STATUS_HIGH => 'High',
    ];

    public const int GENDER_FEMALE = 0;
    public const int GENDER_MALE = 1;

    public const array GENDERS = [
        self::GENDER_FEMALE => 'F',
        self::GENDER_MALE => 'M',
    ];

    public const int TYPE_OF_GAMBLING_CASINO = 1;
    public const int TYPE_OF_GAMBLING_BETTING = 2;
    public const int TYPE_OF_GAMBLING_MIXED = 3;

    public const array TYPES_OF_GAMBLING = [
        self::TYPE_OF_GAMBLING_CASINO => 'Casino',
        self::TYPE_OF_GAMBLING_BETTING => 'Betting',
        self::TYPE_OF_GAMBLING_MIXED => 'Mixed',
    ];

    public const int PERSONAL_MANAGER_NO = 0;

    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $date;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $date_updated;
    #[IpValidator]
    public ?string $ip;
    #[StringLimitedValidator(100)]
    #[FilterValidator([self::class, 'emailToLowerCase'])]
    public ?string $email;
    #[BooleanValidator]
    public ?bool $email_confirm = false;
    #[StringValidator(1, 50)]
    public ?string $phone;
    #[BooleanValidator]
    public ?bool $phone_confirm = false;
    #[StringValidator(2, 2)]
    public ?string $country;
    #[StringLimitedValidator(100)]
    public ?string $login;
    /** @deprecated */
    public ?int $reg_type;
    #[IdValidator]
    public ?int $social_id;
    #[StringValidator(1, 36)]
    public ?string $social_key;
    #[StringLimitedValidator(5)]
    public ?string $locale;
    #[IntValidator]
    public int $personal_manager = 0; // TODO: remove 0 and use NULL
    /** @deprecated  */
    #[StringLimitedValidator(70)]
    public ?string $city;
    #[FilterValidator([self::class, 'filterBirthday'])]
    #[DateValidator]
    public ?string $birthday;
    #[IdValidator]
    public ?int $refcode_id;
    #[IdValidator]
    public ?int $aff_data_id;
    #[IdValidator]
    public ?int $cid;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $email_confirmed_at;
    #[StringLimitedValidator(100)]
    public ?string $comment_withdraw;
    #[BooleanValidator]
    public ?bool $is_blocked = false;
    #[BooleanValidator]
    public ?bool $is_ignore = false;
    #[IdValidator]
    public ?int $useragent_id;
    #[IntInArrayValidator(self::STATUSES)]
    public ?int $status = self::STATUS_FREE;
    #[IntInArrayValidator(self::ACTIVE_STATUSES)]
    public ?int $active_status = self::ACTIVE_STATUS_ACTIVE;
    #[BooleanValidator]
    public ?bool $is_rm = false;
    #[BooleanValidator]
    public ?bool $is_manual_status = false;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $status_updated_at;
    #[EmailValidator(100)]
    public ?string $alt_email;
    #[BooleanValidator]
    public ?bool $alt_email_confirm;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $alt_email_confirmed_at;
    #[IntInArrayValidator(self::GENDERS)]
    public ?int $gender;
    /**
     * @deprecated
     * @see UserKyc::$kyc_status
     */
    public ?bool $is_kyc_confirmed;
    #[StringLimitedValidator(100)]
    public ?string $name;
    #[StringLimitedValidator(200)]
    public ?string $address;
    #[IntInArrayValidator(UserLogin::METHODS)]
    public ?int $registration_method;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $kyc_updated_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $cid_reset_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $phone_confirmed_at;
    #[StringValidator(1, 50)]
    public ?string $uuid;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $source_reset_at;
    #[IdValidator]
    public ?int $host_id;
    #[IntInArrayValidator(self::TYPES_OF_GAMBLING)]
    public ?int $priority_type_of_gambling;
    #[BooleanValidator]
    public bool $is_toxic = false;
    #[IdValidator]
    public ?int $brand_id;
    #[BooleanValidator]
    public bool $is_aff_hidden = false;
    #[IntInArrayValidator(self::BONUS_BL_STATUSES)]
    public int $bbl_status = self::BONUS_BL_STATUS_NO;
    #[IdValidator]
    public ?int $lp_id;
    #[IntArrayValidator]
    public ?array $aff_params;
    #[IdValidator]
    public ?int $city_id;

    public static function actualStatuses(): array
    {
        return array_diff_key(self::STATUSES, array_flip([self::STATUS_PRE_ULTRA, self::STATUS_PREFECT]));
    }

    public static function filterBirthday(mixed $value): mixed
    {
        // 0000-12-15 => 2016-12-15
        if (is_string($value) && str_starts_with($value, '0000')) {
            $value = date('Y') . substr($value, 4);
        }

        return $value;
    }

    public static function emailToLowerCase(?string $value): ?string
    {
        if (empty($value)) {
            return null;
        }

        return mb_strtolower($value);
    }

    public static function getFullStatusExpression(string $alias = 'u'): string
    {
        $activeStatuses = self::ACTIVE_STATUSES;
        $activeStatusExpression = "CASE $alias.active_status ";
        foreach ($activeStatuses as $id => $activeStatus) {
            $activeStatus = $id === self::ACTIVE_STATUS_ACTIVE ? '' : $activeStatus;
            $activeStatusExpression .= "WHEN $id THEN '$activeStatus' ";
        }
        $activeStatusExpression .= 'END';

        $statuses = self::STATUSES;
        $statusExpression = "CASE $alias.status ";
        foreach ($statuses as $id => $status) {
            $statusExpression .= "WHEN $id THEN '$status' ";
        }
        $statusExpression .= "ELSE 'Unknown status' END";

        $rmStatusExpression = "CASE WHEN $alias.is_rm THEN '(RM)' ELSE '' END";

        return "RTRIM(LTRIM(CONCAT_WS(' ', $activeStatusExpression, $statusExpression, $rmStatusExpression)))";
    }

    public static function getBonusBlackListStatus(?int $id): string
    {
        return self::BONUS_BL_STATUSES[$id] ?? self::BONUS_BL_STATUSES[self::BONUS_BL_STATUS_NO];
    }

    public static function cardHref(int $siteId, int $userId): string
    {
        return Url::to('/user/card', ['siteId' => $siteId, 'userId' => $userId]);
    }

    public static function playerHref(int $siteId, int $userId, ?string $tab = null): string
    {
        $path = "/user/player/$siteId-$userId" . ($tab ? '/' . $tab : null);
        return Url::to($path, [], PHP_SAPI === 'cli');
    }

    public static function playerLink(int $siteId, int $userId, ?string $text = null): string
    {
        $text ??= $userId;
        $href = self::playerHref($siteId, $userId);

        return "<a href=\"$href\" target=\"_blank\">$text</a>";
    }

    public static function getFullStatus(array $user): string
    {
        // Check if all necessary keys exists
        if (!Arr::allKeysExists($user, ['status', 'active_status', 'is_rm'])) {
            throw new \RuntimeException('Not all keys present in user for full status');
        }

        $status = self::STATUSES[$user['status']] ?? 'Unknown status';
        $activeStatus = $user['active_status'] === self::ACTIVE_STATUS_ACTIVE ? '' : (self::ACTIVE_STATUSES[$user['active_status']] ?? 'Unknown active status');
        $rm = $user['is_rm'] ? '(RM)' : '';

        return trim("$activeStatus $status $rm");
    }

    public static function getStatusById(?int $id): ?string
    {
        return self::STATUSES[$id] ?? null;
    }

    public static function getActiveStatusById(?int $id): ?string
    {
        return self::ACTIVE_STATUSES[$id] ?? null;
    }
}
