<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250724_075142_loyalty_statuses_add_brand_column extends BaseMigration
{
    public function up(): void
    {
        $this->sql('ALTER TABLE loyalty_statuses ADD COLUMN brand_id integer');
        $this->sql('CREATE UNIQUE INDEX CONCURRENTLY loyalty_statuses_brand_unique ON loyalty_statuses (id, site_id, brand_id) WHERE (brand_id is not null)');
    }

    public function down(): void
    {
        $this->sql("ALTER TABLE loyalty_statuses DROP COLUMN brand_id");
    }
}
