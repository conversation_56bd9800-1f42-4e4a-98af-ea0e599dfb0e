<?php

declare(strict_types=1);

namespace app\back\migrations;

use Yiisoft\Db\Connection\ConnectionInterface;

class m230504_151604_users_documents_faces_similarity_drop_dot
{
    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function up(): void
    {
        $this->db->createCommand()
            ->dropColumn('users_documents_faces_similarity', 'dot')
            ->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()
            ->addColumn('users_documents_faces_similarity', 'dot', 'double not null default 0.')
            ->execute();
    }
}
